# Delhivery Integration Guide

## Overview

This guide provides complete instructions for setting up and using the Delhivery shipping integration in your e-commerce platform.

## Features

✅ **Complete API Integration**
- Real-time shipping rate calculation
- Automatic order creation with Delhivery
- Shipment tracking functionality
- Error handling and logging

✅ **Admin Management**
- Easy configuration interface
- API credentials management
- Test connection functionality
- Fulfillment center address setup

✅ **Order Processing**
- Automatic shipping order creation after payment
- Manual shipping order creation from admin
- Priority-based partner selection
- Comprehensive error handling

## Setup Instructions

### 1. Database Setup

The required database tables are already created through migrations:
- `shipping_partners` - Stores shipping partner information
- `shipping_configurations` - Stores partner-specific configurations
- Orders table has been extended with shipping partner fields

### 2. Configure Delhivery Credentials

1. **Get Delhivery API Credentials:**
   - Sign up at [Delhivery](https://www.delhivery.com/)
   - Get your API Token from the dashboard
   - Note your Client ID (if required)

2. **Configure in Admin Panel:**
   - Go to **Admin → Setup & Configurations → Shipping Partners**
   - Click **Edit** on Delhivery partner
   - Enter your **API Token** in the API Key field
   - Enter your **Client ID** in the API Secret field (if required)
   - Click **Test Connection** to verify credentials
   - Set status to **Active**
   - Click **Save**

### 3. Configure Fulfillment Center

Set up your fulfillment center address in JSON format:

```json
{
    "name": "Your Fulfillment Center Name",
    "address": "Your warehouse address",
    "city": "Your City",
    "state": "Your State",
    "country": "India",
    "postal_code": "110001",
    "phone": "9999999999"
}
```

### 4. Activate the Integration

1. Enable **Use Shipping Rate Calculator** if you want real-time rates
2. Set the shipping partner status to **Active**
3. Ensure the shipping configuration is also **Active**

## Usage

### Automatic Order Creation

Orders are automatically processed when:
1. Customer places an order
2. Payment is confirmed
3. No shipping partner is already assigned

The system will:
- Try each active shipping partner in order
- Create shipping order with the first successful partner
- Update order with tracking information
- Log all activities for debugging

### Manual Order Creation

From the admin panel:
1. Go to **Sales → Orders**
2. Click on an order to view details
3. Select **Delhivery** from the shipping partner dropdown
4. Click **Create Shipping Order**
5. System will create the order and update tracking information

### Tracking Orders

Track shipments using:
- Order details page in admin
- Customer order tracking page
- Direct API calls to tracking service

## API Endpoints Used

### Rate Calculation
```
GET /api/kinko/v1/invoice/charges/.json
```

### Order Creation
```
POST /api/cmu/create.json
```

### Tracking
```
GET /api/v1/packages/json/
```

## Configuration Options

### Shipping Partner Settings
- **Name**: Delhivery (read-only)
- **Code**: delhivery (read-only)
- **API Key**: Your Delhivery API Token
- **API Secret**: Your Client ID (optional)
- **API URL**: https://track.delhivery.com/api (auto-configured)
- **Status**: Active/Inactive

### Shipping Configuration
- **Is Active**: Enable/disable this partner
- **Use Shipping Rate Calculator**: Enable real-time rate calculation
- **Cloud Mart Fulfillment Address**: JSON formatted address

## Testing

### Test Connection
Use the **Test Connection** button in the admin panel to verify:
- API credentials are valid
- API endpoints are accessible
- Basic functionality is working

### Test Script
Run the included test script:
```bash
php test_delhivery_integration.php
```

This will verify:
- Database setup
- Service instantiation
- API credentials
- Rate calculation
- Order creation structure

## Troubleshooting

### Common Issues

1. **"API Token is not configured"**
   - Solution: Add your Delhivery API Token in shipping partner settings

2. **"Invalid API credentials"**
   - Solution: Verify your API token is correct and active
   - Check if your Delhivery account is properly set up

3. **"No active shipping partners available"**
   - Solution: Activate the Delhivery partner and its configuration

4. **"Missing required address field"**
   - Solution: Ensure all required address fields are filled
   - Check fulfillment center address configuration

5. **"Failed to create order with Delhivery"**
   - Check logs for detailed error messages
   - Verify address formats and postal codes
   - Ensure product weights are set

### Logging

All activities are logged for debugging:
- Rate calculation requests/responses
- Order creation attempts
- API errors and exceptions
- Tracking requests

Check Laravel logs at `storage/logs/laravel.log`

## Support

For technical issues:
1. Check the logs for detailed error messages
2. Verify all configuration settings
3. Test with the provided test script
4. Contact Delhivery support for API-related issues

## Security Notes

- API tokens are stored securely in the database
- All API communications use HTTPS
- Sensitive data is logged appropriately
- Input validation is performed on all data

## Performance

- Rate calculations are cached when possible
- API timeouts are handled gracefully
- Fallback mechanisms are in place
- Database queries are optimized
