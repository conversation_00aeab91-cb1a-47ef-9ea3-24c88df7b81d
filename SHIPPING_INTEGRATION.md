# Shipping Integration Documentation

## Overview

This document describes the complete shipping integration implementation for Cloud Mart e-commerce platform with Shiprocket, Delhivery, and Ecom Express delivery partners.

## Features Implemented

### ✅ Real API Integrations
- **Shiprocket**: Complete API integration with authentication, rate calculation, order creation, and tracking
- **Delhivery**: Full API integration with rate calculation, waybill generation, and tracking
- **Ecom Express**: Complete API integration with rate calculation, order booking, and tracking

### ✅ Admin Management
- Shipping partner configuration interface
- API credentials management
- Status toggle functionality
- Manual shipping order creation from order details

### ✅ Automatic Order Processing
- Auto-create shipping orders after payment confirmation
- Priority-based partner selection
- Fallback mechanism if one partner fails

### ✅ Order Flow Integration
- Shipping partner assignment during order creation
- Tracking ID generation and storage
- Order status updates

### ✅ SMS & WhatsApp Notifications
- Automatic SMS and WhatsApp notifications for all order events
- Configurable notification settings in admin panel
- Template-based WhatsApp messaging via BotSailor API
- Test notification functionality
- Elegant fallback mechanism (if one fails, the other still works)

## Setup Instructions

### 1. Database Setup

Run the migrations and seeder:

```bash
php artisan migrate
php artisan db:seed --class=ShippingPartnerSeeder
```

### 2. Environment Configuration

Add the following to your `.env` file:

```env
# Shipping Partners
SHIPROCKET_EMAIL="your-shiprocket-email"
SHIPROCKET_PASSWORD="your-shiprocket-password"
SHIPROCKET_API_URL="https://apiv2.shiprocket.in/v1/external"

DELHIVERY_API_KEY="your-delhivery-api-key"
DELHIVERY_API_URL="https://track.delhivery.com/api"

ECOM_EXPRESS_USERNAME="your-ecom-express-username"
ECOM_EXPRESS_PASSWORD="your-ecom-express-password"
ECOM_EXPRESS_API_URL="https://api.ecomexpress.in"

# WhatsApp Notifications
WHATSAPP_API_TOKEN="6723|qSDJOCP28LCAVLVl1ik4QzkhlQ9oMTGscJLWzJxB"
WHATSAPP_PHONE_NUMBER_ID="383709501489036"
WHATSAPP_API_URL="https://botsailor.com/api/v1/whatsapp/send/template"
WHATSAPP_TEMPLATE_ORDER_PLACED="122343"
WHATSAPP_TEMPLATE_ORDER_CONFIRMED="122344"
WHATSAPP_TEMPLATE_ORDER_SHIPPED="122345"
WHATSAPP_TEMPLATE_ORDER_DELIVERED="122346"
WHATSAPP_TEMPLATE_PAYMENT_CONFIRMED="122347"
```

### 3. Notification Configuration

1. Go to **Setup & Configurations > Notification Settings**
2. Enable/disable SMS and WhatsApp notifications
3. Test notifications with your phone number
4. Configure WhatsApp templates in your BotSailor account

### 4. Admin Configuration

1. Go to **Setup & Configurations > Shipping Partners**
2. Configure API credentials for each partner:
   - **Shiprocket**: Email and Password
   - **Delhivery**: API Key
   - **Ecom Express**: Username and Password
3. Set API URLs (pre-configured)
4. Activate the partners you want to use
5. Configure fulfillment center address

## API Credentials Setup

### Shiprocket
1. Sign up at [Shiprocket](https://www.shiprocket.in/)
2. Get your login email and password
3. Use these as API credentials

### Delhivery
1. Sign up at [Delhivery](https://www.delhivery.com/)
2. Get your API token from the dashboard
3. Use the token as API Key

### Ecom Express
1. Sign up at [Ecom Express](https://www.ecomexpress.in/)
2. Get your username and password for API access
3. Use these credentials

### WhatsApp (BotSailor)
1. Sign up at [BotSailor](https://botsailor.com/)
2. Get your API token and phone number ID
3. Create WhatsApp message templates for each notification type
4. Configure template IDs in environment variables

## How It Works

### Order Creation Flow

1. **Customer places order** → Order created in database
2. **Payment confirmed** → `CheckoutController::checkout_done()` called
3. **Auto shipping order creation** → `OrderController::autoCreateShippingOrders()` called
4. **Partner selection** → `OrderShippingService::createShippingOrder()` tries each active partner
5. **API call** → Creates shipping order with selected partner
6. **Order updated** → Tracking ID and partner details saved

### Manual Shipping Order Creation

1. Admin goes to **Sales > Orders > Order Details**
2. Selects shipping partner from dropdown
3. Clicks "Create Shipping Order" button
4. System creates shipping order via API
5. Order updated with tracking details

### Rate Calculation

The system can calculate shipping rates from all active partners for comparison:

```php
$orderShippingService = new OrderShippingService();
$rates = $orderShippingService->getShippingRates($order);
```

### Tracking

Track shipments using:

```php
$orderShippingService = new OrderShippingService();
$trackingData = $orderShippingService->trackShipment($order);
```

## Testing

Use the built-in test command:

```bash
# Test all active partners
php artisan shipping:test

# Test specific partner
php artisan shipping:test shiprocket

# Test with real order
php artisan shipping:test shiprocket --order-id=123
```

## API Endpoints

### Shiprocket
- **Authentication**: POST `/auth/login`
- **Rate Calculation**: GET `/courier/serviceability`
- **Create Order**: POST `/orders/create/adhoc`
- **Track Shipment**: GET `/courier/track/shipment/{tracking_id}`

### Delhivery
- **Rate Calculation**: GET `/kinko/v1/invoice/charges/.json`
- **Create Order**: POST `/cmu/create.json`
- **Track Shipment**: GET `/v1/packages/json/`

### Ecom Express
- **Rate Calculation**: POST `/services/shipment/products/v2/fetch_awb_charges`
- **Create Order**: POST `/services/shipment/products/v2/fetch_awb`
- **Track Shipment**: POST `/track_me/api/mawbd`

## Error Handling

- All API calls include proper error handling
- Failed attempts are logged
- Fallback to next available partner
- User-friendly error messages

## Fulfillment Types

The system supports two fulfillment types:

1. **Cloud Mart Fulfillment**: Products shipped from Cloud Mart warehouse
2. **Vendor Fulfillment**: Products shipped from vendor location

## Priority Logic

Partners are tried in the order they appear in the database. You can modify the priority by:

1. Adding a `priority` field to the `shipping_partners` table
2. Updating the query in `OrderShippingService::createShippingOrder()`

## Troubleshooting

### Common Issues

1. **Authentication Failed**: Check API credentials
2. **Rate Calculation Failed**: Verify postal codes and addresses
3. **Order Creation Failed**: Check required fields and API limits
4. **No Active Partners**: Activate at least one partner in admin panel

### Logs

Check Laravel logs for detailed error information:

```bash
tail -f storage/logs/laravel.log
```

## Support

For issues with specific shipping partners:

- **Shiprocket**: [Support](https://support.shiprocket.in/)
- **Delhivery**: [Support](https://www.delhivery.com/contact-us/)
- **Ecom Express**: [Support](https://www.ecomexpress.in/contact-us/)
