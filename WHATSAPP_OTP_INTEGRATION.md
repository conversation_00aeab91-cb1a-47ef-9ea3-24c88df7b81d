# WhatsApp OTP Integration

## Overview

Simple WhatsApp OTP integration using BotSailor API for phone number authentication. Users can now receive OTP via both SMS and WhatsApp when logging in with their phone number.

## Features

- **Dual OTP Delivery**: Automatically sends OTP via both SMS and WhatsApp
- **Simple Integration**: Minimal changes to existing codebase
- **Flexible Configuration**: Easy to enable/disable via environment variables
- **Error Handling**: Graceful fallback if WhatsA<PERSON> fails, SMS still works

## Setup

### 1. Environment Configuration

Add these variables to your `.env` file:

```env
# WhatsApp OTP via BotSailor
WHATSAPP_API_TOKEN="6723|qSDJOCP28LCAVLVl1ik4QzkhlQ9oMTGscJLWzJxB"
WHATSAPP_PHONE_NUMBER_ID="***************"
WHATSAPP_OTP_TEMPLATE_ID="122343"
```

### 2. BotSailor Template Setup

Create a WhatsApp template in your BotSailor account with:
- Template ID: `122343` (or update the env variable)
- Template content: Your OTP message with variable placeholder
- Variable: `{{1}}` for the OTP code

Example template:
```
Your OTP is {{1}}. Do not share this code with anyone.
```

## How It Works

### Login Flow

1. **User enters phone number** → Clicks "Send OTP"
2. **System generates OTP** → Stores in cache for 10 minutes
3. **SMS sent** → Via existing SMS provider (Twilio, etc.)
4. **WhatsApp sent** → Via BotSailor API simultaneously
5. **User enters OTP** → Validates against cached OTP
6. **Login successful** → User authenticated

### Password Reset Flow

1. **User enters phone number** → Clicks "Reset Password"
2. **System generates verification code** → Stores in database
3. **SMS + WhatsApp sent** → Both channels deliver the code
4. **User enters code** → Validates and allows password reset

## API Integration

### BotSailor API Call

```php
POST https://botsailor.com/api/v1/whatsapp/send/template

Parameters:
- apiToken: Your BotSailor API token
- phone_number_id: Your WhatsApp phone number ID
- template_id: Your template ID
- phone_number: Recipient phone number (without +)
- templateVariable-code-1: The OTP code
```

### Phone Number Formatting

- Input: `+919999999999` or `919999999999`
- Processed: `919999999999` (removes + and spaces)
- Sent to API: `919999999999`

## Files Modified

### Core Service
- `app/Services/WhatsAppOtpService.php` - Main WhatsApp OTP service

### Controllers Updated
- `app/Http/Controllers/HomeController.php` - Login OTP sending
- `app/Http/Controllers/Auth/ForgotPasswordController.php` - Password reset OTP

### Utilities Updated
- `app/Utility/SendSMSUtility.php` - Added dual SMS+WhatsApp method

### Configuration
- `.env.example` - Added WhatsApp configuration variables

## Usage Examples

### Send OTP for Login
```php
// In HomeController::sendOtp()
$whatsappService = new WhatsAppOtpService();
if ($whatsappService->isEnabled()) {
    $whatsappService->sendOtp($phone, $otp);
}
```

### Send OTP for Password Reset
```php
// In ForgotPasswordController
SendSMSUtility::sendOtpViaBoth($phone, $user->verification_code);
```

### Check if WhatsApp is Enabled
```php
$whatsappService = new WhatsAppOtpService();
if ($whatsappService->isEnabled()) {
    // WhatsApp is configured and ready
}
```

## Error Handling

- **WhatsApp fails**: SMS still works, user gets OTP
- **SMS fails**: WhatsApp still works, user gets OTP
- **Both fail**: Error logged, user notified to try again
- **Invalid template**: Logged error, falls back to SMS only

## Logging

All WhatsApp OTP attempts are logged:

```php
// Success
Log::info('WhatsApp OTP sent successfully', [
    'phone' => $phone,
    'otp' => $otp
]);

// Failure
Log::error('WhatsApp OTP failed', [
    'phone' => $phone,
    'response' => $response->body(),
    'status' => $response->status()
]);
```

## Testing

### Test WhatsApp OTP
1. Configure your phone number in BotSailor
2. Set up the template with your phone number
3. Try login with phone number
4. Check if you receive both SMS and WhatsApp messages

### Verify Logs
```bash
tail -f storage/logs/laravel.log | grep "WhatsApp OTP"
```

## Troubleshooting

### Common Issues

1. **No WhatsApp received**:
   - Check API token and phone number ID
   - Verify template ID exists in BotSailor
   - Check phone number format (no + prefix)

2. **Template not found**:
   - Verify template ID in BotSailor dashboard
   - Update `WHATSAPP_OTP_TEMPLATE_ID` in .env

3. **API authentication failed**:
   - Check `WHATSAPP_API_TOKEN` is correct
   - Verify token has necessary permissions

### Debug Mode

Enable detailed logging by checking Laravel logs:
```bash
tail -f storage/logs/laravel.log
```

## Security Notes

- OTP expires after 10 minutes
- WhatsApp template should include security warning
- Phone numbers are validated before sending
- API tokens should be kept secure

## Future Extensions

This simple implementation can be extended to:
- Support multiple WhatsApp templates
- Add delivery status tracking
- Implement retry mechanisms
- Add admin configuration interface
- Support other notification events

## Support

For BotSailor API issues:
- Documentation: https://botsailor.com/docs
- Support: Contact BotSailor support team

For integration issues:
- Check Laravel logs
- Verify environment configuration
- Test with known working phone numbers
