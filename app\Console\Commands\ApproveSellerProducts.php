<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product;
use App\Models\User;
use Illuminate\Support\Facades\Artisan;

class ApproveSellerProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'product:approve-seller {user_id?} {--all-verified}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Approve products for verified sellers';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        $allVerified = $this->option('all-verified');

        if ($allVerified) {
            $this->approveAllVerifiedSellerProducts();
        } elseif ($userId) {
            $this->approveSellerProducts($userId);
        } else {
            $this->error('Please provide either a user_id or use --all-verified flag');
            return 1;
        }

        return 0;
    }

    private function approveSellerProducts($userId)
    {
        $user = User::find($userId);
        if (!$user || $user->user_type !== 'seller') {
            $this->error("Seller with ID {$userId} not found");
            return;
        }

        if (!$user->shop || $user->shop->verification_status != 1) {
            $this->error("Seller {$user->name} is not verified");
            return;
        }

        $products = Product::where('user_id', $userId)->where('approved', 0)->get();
        
        if ($products->count() == 0) {
            $this->info("No pending products found for seller {$user->name}");
            return;
        }

        $this->info("Approving {$products->count()} products for seller {$user->name}...");

        foreach ($products as $product) {
            $product->approved = 1;
            $product->save();
            $this->info("✓ Approved: {$product->name}");
        }

        // Clear caches
        Artisan::call('view:clear');
        Artisan::call('cache:clear');

        $this->info("All products approved successfully!");
    }

    private function approveAllVerifiedSellerProducts()
    {
        $verifiedSellers = User::where('user_type', 'seller')
            ->whereHas('shop', function($query) {
                $query->where('verification_status', 1);
            })
            ->with('products')
            ->get();

        $totalApproved = 0;

        foreach ($verifiedSellers as $seller) {
            $pendingProducts = $seller->products->where('approved', 0);
            
            if ($pendingProducts->count() > 0) {
                $this->info("Approving {$pendingProducts->count()} products for {$seller->name}...");
                
                foreach ($pendingProducts as $product) {
                    $product->approved = 1;
                    $product->save();
                    $totalApproved++;
                }
            }
        }

        if ($totalApproved > 0) {
            // Clear caches
            Artisan::call('view:clear');
            Artisan::call('cache:clear');
            
            $this->info("Total products approved: {$totalApproved}");
        } else {
            $this->info("No pending products found for verified sellers");
        }
    }
}
