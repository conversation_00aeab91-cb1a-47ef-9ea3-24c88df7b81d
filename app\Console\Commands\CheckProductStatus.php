<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product;
use App\Models\User;

class CheckProductStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'product:check-status {user_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check product approval status for sellers';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $userId = $this->argument('user_id');

        $this->info("Product Approval Setting: " . (get_setting('product_approve_by_admin') ? 'ENABLED' : 'DISABLED'));

        if ($userId) {
            $this->checkSellerProducts($userId);
        } else {
            $this->checkAllSellerProducts();
        }

        return 0;
    }

    private function checkSellerProducts($userId)
    {
        $user = User::find($userId);
        if (!$user || $user->user_type !== 'seller') {
            $this->error("Seller with ID {$userId} not found");
            return;
        }

        $products = Product::where('user_id', $userId)->get();
        
        $this->info("Products for Seller ID {$userId} ({$user->name}):");
        $this->info("Total Products: " . $products->count());

        foreach ($products as $product) {
            $approvalStatus = $product->approved ? 'APPROVED' : 'PENDING';
            $publishedStatus = $product->published ? 'PUBLISHED' : 'UNPUBLISHED';
            $this->info("ID: {$product->id} | {$product->name} | {$approvalStatus} | {$publishedStatus}");
        }
    }

    private function checkAllSellerProducts()
    {
        $this->info("All Seller Products Status:");
        
        $sellers = User::where('user_type', 'seller')->with('products')->get();
        
        foreach ($sellers as $seller) {
            if ($seller->products->count() > 0) {
                $approved = $seller->products->where('approved', 1)->count();
                $pending = $seller->products->where('approved', 0)->count();
                $published = $seller->products->where('published', 1)->count();
                
                $this->info("Seller: {$seller->name} | Total: {$seller->products->count()} | Approved: {$approved} | Pending: {$pending} | Published: {$published}");
            }
        }
    }
}
