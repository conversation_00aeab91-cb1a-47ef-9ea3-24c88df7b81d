<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use App\Models\Shop;
use App\Models\User;

class CheckSellerStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seller:check-status {user_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check seller verification status and cache status';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $userId = $this->argument('user_id');

        if ($userId) {
            $this->checkSpecificSeller($userId);
        } else {
            $this->checkAllSellers();
        }

        $this->checkCacheStatus();

        return 0;
    }

    private function checkSpecificSeller($userId)
    {
        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return;
        }

        if ($user->user_type !== 'seller') {
            $this->error("User with ID {$userId} is not a seller");
            return;
        }

        $shop = $user->shop;
        if (!$shop) {
            $this->error("No shop found for user {$userId}");
            return;
        }

        $this->info("Seller Details:");
        $this->info("User ID: {$user->id}");
        $this->info("Name: {$user->name}");
        $this->info("Email: {$user->email}");
        $this->info("Shop Name: {$shop->name}");
        $this->info("Verification Status: " . ($shop->verification_status ? 'APPROVED' : 'PENDING'));
        $this->info("Banned: " . ($user->banned ? 'YES' : 'NO'));
    }

    private function checkAllSellers()
    {
        $this->info("All Sellers Status:");
        $sellers = User::where('user_type', 'seller')->with('shop')->get();
        
        foreach ($sellers as $seller) {
            if ($seller->shop) {
                $status = $seller->shop->verification_status ? 'APPROVED' : 'PENDING';
                $banned = $seller->banned ? 'BANNED' : 'ACTIVE';
                $this->info("ID: {$seller->id} | {$seller->name} | {$status} | {$banned}");
            }
        }
    }

    private function checkCacheStatus()
    {
        $this->info("\nCache Status:");
        
        $verifiedSellers = Cache::get('verified_sellers_id');
        $this->info("Verified Sellers Cache: " . ($verifiedSellers ? count($verifiedSellers) . ' sellers' : 'NOT CACHED'));
        
        $unbannedSellers = Cache::get('unbanned_sellers_id');
        $this->info("Unbanned Sellers Cache: " . ($unbannedSellers ? count($unbannedSellers) . ' sellers' : 'NOT CACHED'));

        if ($verifiedSellers) {
            $this->info("Verified Seller IDs: " . implode(', ', $verifiedSellers));
        }
    }
}
