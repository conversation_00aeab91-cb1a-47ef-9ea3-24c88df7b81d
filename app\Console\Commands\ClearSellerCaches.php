<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use App\Models\Category;

class ClearSellerCaches extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seller:clear-caches';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all seller verification related caches';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Clearing seller verification caches...');

        // Clear verified sellers cache
        Cache::forget('verified_sellers_id');
        $this->info('✓ Cleared verified_sellers_id cache');

        // Clear unbanned sellers cache
        Cache::forget('unbanned_sellers_id');
        $this->info('✓ Cleared unbanned_sellers_id cache');

        // Clear all product category caches
        $categories = Category::pluck('id');
        foreach ($categories as $categoryId) {
            Cache::forget('products-category-' . $categoryId);
        }
        Cache::forget('products-category-null');
        $this->info('✓ Cleared product category caches');

        // Clear view and application caches
        $this->call('view:clear');
        $this->call('cache:clear');
        $this->info('✓ Cleared view and application caches');

        $this->info('All seller verification caches cleared successfully!');
        return 0;
    }
}
