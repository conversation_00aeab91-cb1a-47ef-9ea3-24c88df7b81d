<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Services\ProductService;

class TestAutoApproval extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:auto-approval {user_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test auto-approval logic for verified sellers';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        $user = User::find($userId);

        if (!$user || $user->user_type !== 'seller') {
            $this->error("Seller with ID {$userId} not found");
            return 1;
        }

        $this->info("Testing auto-approval logic for seller: {$user->name}");
        $this->info("User ID: {$user->id}");
        $this->info("User Type: {$user->user_type}");
        
        if ($user->shop) {
            $this->info("Shop Name: {$user->shop->name}");
            $this->info("Shop Verification Status: " . ($user->shop->verification_status ? 'VERIFIED' : 'UNVERIFIED'));
        } else {
            $this->error("No shop found for this seller");
            return 1;
        }

        $this->info("Product Approve by Admin Setting: " . (get_setting('product_approve_by_admin') ? 'ENABLED' : 'DISABLED'));

        // Test the logic
        $approved = 1;
        if (get_setting('product_approve_by_admin') == 1) {
            if ($user->shop && $user->shop->verification_status == 1) {
                $approved = 1; // Auto-approve for verified sellers
                $this->info("✅ RESULT: Products will be AUTO-APPROVED for this verified seller");
            } else {
                $approved = 0; // Require approval for unverified sellers
                $this->info("❌ RESULT: Products will require MANUAL APPROVAL for this unverified seller");
            }
        } else {
            $this->info("✅ RESULT: Products will be AUTO-APPROVED (admin approval disabled)");
        }

        return 0;
    }
}
