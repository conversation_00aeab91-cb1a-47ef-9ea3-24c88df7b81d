<?php

namespace App\Helpers;

use Spatie\Permission\Models\Role;
use Spatie\Permission\Exceptions\PermissionDoesNotExist;

class PermissionHelper
{
    /**
     * Safely check if a role has a permission without throwing exceptions
     *
     * @param Role $role
     * @param string $permissionName
     * @return bool
     */
    public static function roleHasPermission(Role $role, string $permissionName): bool
    {
        try {
            return $role->hasPermissionTo($permissionName);
        } catch (PermissionDoesNotExist $e) {
            // Permission doesn't exist for this guard, return false
            return false;
        } catch (\Exception $e) {
            // Any other exception, return false for safety
            return false;
        }
    }

    /**
     * Safely check if a user has a permission without throwing exceptions
     *
     * @param \App\Models\User $user
     * @param string $permissionName
     * @return bool
     */
    public static function userHasPermission($user, string $permissionName): bool
    {
        try {
            return $user->hasPermissionTo($permissionName);
        } catch (PermissionDoesNotExist $e) {
            // Permission doesn't exist for this guard, return false
            return false;
        } catch (\Exception $e) {
            // Any other exception, return false for safety
            return false;
        }
    }

    /**
     * Get all permissions that exist for the web guard
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getAllWebPermissions()
    {
        return \Spatie\Permission\Models\Permission::where('guard_name', 'web')->get();
    }

    /**
     * Get all roles that exist for the web guard
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getAllWebRoles()
    {
        return \Spatie\Permission\Models\Role::where('guard_name', 'web')->get();
    }
}
