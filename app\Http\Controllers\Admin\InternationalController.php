<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\WhatsAppOtpService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class InternationalController extends Controller
{
    protected $whatsappService;

    public function __construct()
    {
        $this->whatsappService = new WhatsAppOtpService();
    }

    /**
     * Display international settings dashboard
     */
    public function index()
    {
        $supportedCountries = $this->whatsappService->getSupportedCountriesList();
        $expansionPhases = config('international.expansion_phases');
        $featureFlags = config('international.feature_flags');
        
        return view('admin.international.index', compact(
            'supportedCountries', 
            'expansionPhases', 
            'featureFlags'
        ));
    }

    /**
     * Display WhatsApp OTP country management
     */
    public function whatsappCountries()
    {
        $countries = config('international.whatsapp_countries');
        $activeCountries = array_filter($countries, function($country) {
            return $country['active'];
        });
        
        return view('admin.international.whatsapp_countries', compact(
            'countries', 
            'activeCountries'
        ));
    }

    /**
     * Enable WhatsApp OTP for a country
     */
    public function enableWhatsappCountry(Request $request)
    {
        $countryCode = $request->country_code;
        $countries = config('international.whatsapp_countries');
        
        if (!isset($countries[$countryCode])) {
            return response()->json([
                'success' => false,
                'message' => 'Country not found'
            ], 404);
        }

        // In production, this should update database/config file
        // For now, we'll use cache
        $countries[$countryCode]['active'] = true;
        Cache::put('whatsapp_countries_override', $countries, now()->addDays(30));
        
        Log::info("WhatsApp OTP enabled for country", [
            'country_code' => $countryCode,
            'country_name' => $countries[$countryCode]['name'],
            'admin_user' => auth()->user()->name ?? 'System'
        ]);

        return response()->json([
            'success' => true,
            'message' => "WhatsApp OTP enabled for {$countries[$countryCode]['name']}"
        ]);
    }

    /**
     * Disable WhatsApp OTP for a country
     */
    public function disableWhatsappCountry(Request $request)
    {
        $countryCode = $request->country_code;
        $countries = config('international.whatsapp_countries');
        
        if (!isset($countries[$countryCode])) {
            return response()->json([
                'success' => false,
                'message' => 'Country not found'
            ], 404);
        }

        // In production, this should update database/config file
        $countries[$countryCode]['active'] = false;
        Cache::put('whatsapp_countries_override', $countries, now()->addDays(30));
        
        Log::info("WhatsApp OTP disabled for country", [
            'country_code' => $countryCode,
            'country_name' => $countries[$countryCode]['name'],
            'admin_user' => auth()->user()->name ?? 'System'
        ]);

        return response()->json([
            'success' => true,
            'message' => "WhatsApp OTP disabled for {$countries[$countryCode]['name']}"
        ]);
    }

    /**
     * Test WhatsApp OTP for a country
     */
    public function testWhatsappOtp(Request $request)
    {
        $request->validate([
            'country_code' => 'required|string',
            'phone_number' => 'required|string',
        ]);

        $testOtp = '123456'; // Test OTP
        $result = $this->whatsappService->sendOtp(
            $request->phone_number, 
            $testOtp, 
            $request->country_code
        );

        return response()->json([
            'success' => $result['success'],
            'message' => $result['message'],
            'details' => $result
        ]);
    }

    /**
     * Get expansion roadmap
     */
    public function expansionRoadmap()
    {
        $phases = config('international.expansion_phases');
        $countries = config('international.whatsapp_countries');
        
        $roadmap = [];
        foreach ($phases as $phaseKey => $phase) {
            $phaseCountries = [];
            foreach ($phase['countries'] as $countryCode) {
                if (isset($countries[$countryCode])) {
                    $phaseCountries[] = $countries[$countryCode];
                }
            }
            $roadmap[$phaseKey] = array_merge($phase, ['country_details' => $phaseCountries]);
        }
        
        return view('admin.international.roadmap', compact('roadmap'));
    }

    /**
     * Update feature flags
     */
    public function updateFeatureFlags(Request $request)
    {
        $flags = $request->only([
            'multi_currency',
            'multi_language', 
            'international_shipping',
            'local_payment_gateways',
            'whatsapp_business_api'
        ]);

        // In production, update .env file or database
        foreach ($flags as $flag => $value) {
            Cache::put("feature_flag_{$flag}", $value, now()->addDays(30));
        }

        Log::info("Feature flags updated", [
            'flags' => $flags,
            'admin_user' => auth()->user()->name ?? 'System'
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Feature flags updated successfully'
        ]);
    }

    /**
     * Get WhatsApp OTP statistics
     */
    public function whatsappStats()
    {
        // This would typically come from database analytics
        $stats = [
            'total_countries_supported' => count(config('international.whatsapp_countries')),
            'active_countries' => count($this->whatsappService->getSupportedCountriesList()),
            'messages_sent_today' => Cache::get('whatsapp_messages_today', 0),
            'success_rate' => Cache::get('whatsapp_success_rate', 95.5),
            'top_countries' => [
                ['name' => 'India', 'code' => '91', 'messages' => 1250],
                ['name' => 'UAE', 'code' => '971', 'messages' => 45],
                ['name' => 'Singapore', 'code' => '65', 'messages' => 23],
            ]
        ];

        return response()->json($stats);
    }
}
