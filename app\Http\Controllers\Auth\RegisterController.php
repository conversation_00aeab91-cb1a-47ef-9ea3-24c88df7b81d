<?php

namespace App\Http\Controllers\Auth;

use Nexmo;
use Cookie;
use Session;
use App\Models\Cart;
use App\Models\User;
use Twi<PERSON>\Rest\Client;

use App\Rules\Recaptcha;
use Illuminate\Validation\Rule;

use App\Models\Customer;
use App\OtpConfiguration;
use Illuminate\Http\Request;
use App\Models\BusinessSetting;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Validator;
use Illuminate\Foundation\Auth\RegistersUsers;
use App\Utility\SendSMSUtility;
use App\Notifications\EmailVerificationNotification;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = '/';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @param  array  $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:6|confirmed',
            'g-recaptcha-response' => [
                Rule::when(get_setting('google_recaptcha') == 1, ['required', new Recaptcha()], ['sometimes'])
            ]
        ];

        // Add phone validation if OTP system is enabled
        if (get_setting('otp_system')) {
            $rules['phone'] = 'required|string';
            $rules['country_code'] = 'required|string';
        }

        return Validator::make($data, $rules);
    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @param  array  $data
     * @return \App\Models\User
     */
    protected function create(array $data)
    {
        $userData = [
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
        ];

        // Add phone data if OTP system is enabled
        if (get_setting('otp_system') && isset($data['phone'])) {
            $countryCode = '+' . ltrim($data['country_code'] ?? '91', '+');
            // Remove country code from phone if it exists
            $phone = str_replace($countryCode, '', $data['phone']);

            $userData['phone'] = $phone;
            $userData['country_code'] = $countryCode;
            $userData['verification_code'] = rand(100000, 999999);
        }

        $user = User::create($userData);

        // Send OTP if phone is provided and OTP system is enabled
        if (get_setting('otp_system') && isset($data['phone'])) {
            $phone = $userData['phone'];
            $countryCode = $userData['country_code'];
            SendSMSUtility::sendOtpViaBoth($phone, $userData['verification_code'], $countryCode);
        }
        
        if(session('temp_user_id') != null){
            Cart::where('temp_user_id', session('temp_user_id'))
                    ->update([
                        'user_id' => $user->id,
                        'temp_user_id' => null
            ]);

            Session::forget('temp_user_id');
        }

        if(Cookie::has('referral_code')){
            $referral_code = Cookie::get('referral_code');
            $referred_by_user = User::where('referral_code', $referral_code)->first();
            if($referred_by_user != null){
                $user->referred_by = $referred_by_user->id;
                $user->save();
            }
        }

        return $user;
    }

    public function register(Request $request)
    {
        // Check email uniqueness
        if(User::where('email', $request->email)->first() != null){
            flash(translate('Email already exists.'));
            return back();
        }

        // Check phone uniqueness if OTP system is enabled and phone is provided
        if (get_setting('otp_system') && $request->phone) {
            $countryCode = '+' . ltrim($request->country_code ?? '91', '+');
            $phone = str_replace($countryCode, '', $request->phone);

            if (User::where('phone', $phone)
                     ->where('country_code', $countryCode)
                     ->first() != null) {
                flash(translate('Phone number already exists.'));
                return back();
            }
        }

        $this->validator($request->all())->validate();

        $user = $this->create($request->all());

        $this->guard()->login($user);

        if($user->email != null){
            if(BusinessSetting::where('type', 'email_verification')->first()->value != 1){
                $user->email_verified_at = date('Y-m-d H:m:s');
                $user->save();
                offerUserWelcomeCoupon();
                flash(translate('Registration successful.'))->success();
            }
            else {
                try {
                    $user->sendEmailVerificationNotification();
                    flash(translate('Registration successful. Please verify your email.'))->success();
                } catch (\Throwable $th) {
                    $user->delete();
                    flash(translate('Registration failed. Please try again later.'))->error();
                }
            }
        }

        return $this->registered($request, $user)
            ?: redirect($this->redirectPath());
    }

    protected function registered(Request $request, $user)
    {
        // If OTP system is enabled and user has phone but not verified, redirect to verification
        if (get_setting('otp_system') && $user->phone && !$user->phone_verified_at) {
            return redirect()->route('phone.verification');
        }
        // If user has no email (phone-only registration), redirect to verification
        elseif ($user->email == null) {
            return redirect()->route('phone.verification');
        }
        // If there's a session link, redirect there
        elseif(session('link') != null){
            return redirect(session('link'));
        }
        // Default redirect to home
        else {
            return redirect()->route('home');
        }
    }
}
