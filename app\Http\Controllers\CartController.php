<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Cart;
use Auth;
use App\Utility\CartUtility;
use Session;

class CartController extends Controller
{
    public function index(Request $request)
    {
        // Transfer cart from temp user to authenticated user if logged in
        if (auth()->check()) {
            $this->migrateTempCartToUser(auth()->user()->id, $request->session()->get('temp_user_id'));
        }

        // Retrieve carts based on user or temp user id
        $carts = $this->getCarts($request);

        return view('frontend.view_cart', compact('carts'));
    }

    public function showCartModal(Request $request)
    {
        $product = Product::with(['main_category'])->find($request->id);
        return view('frontend.' . get_setting('homepage_select') . '.partials.addToCart', compact('product'));
    }

    public function showCartModalAuction(Request $request)
    {
        $product = Product::find($request->id);
        return view('auction.frontend.addToCartAuction', compact('product'));
    }

    public function addToCart(Request $request)
    {
        $userId = auth()->check() ? auth()->id() : null;

        // Check if user is a guest and ensure temp_user_id is set in the session
        if (!$userId && !$request->session()->has('temp_user_id')) {
            $tempUserId = uniqid('guest_', true);
            $request->session()->put('temp_user_id', $tempUserId);
        } else {
            $tempUserId = $request->session()->get('temp_user_id');
        }

        // Retrieve carts for the user or temp user
        $carts = Cart::where('user_id', $userId)
            ->orWhere('temp_user_id', $tempUserId)
            ->get();

        $check_auction_in_cart = CartUtility::check_auction_in_cart($carts);
        $product = Product::find($request->id);

        // Check auction conditions
        if ($check_auction_in_cart && $product->auction_product == 0) {
            return $this->responseArray(0, $carts, 'removeAuctionProductFromCart');
        }

        // Check quantity requirements
        $quantity = $request['quantity'];
        if ($quantity < $product->min_qty) {
            return $this->responseArray(0, $carts, 'minQtyNotSatisfied', ['min_qty' => $product->min_qty]);
        }

        // Check product stock and create cart item
        $str = CartUtility::create_cart_variant($product, $request->all());
        $product_stock = $product->stocks->where('variant', $str)->first();
        $cart = Cart::firstOrNew([
            'variation' => $str,
            'user_id' => $userId,
            'temp_user_id' => $tempUserId,
            'product_id' => $request['id']
        ]);

        // Handle existing cart item updates
        if ($cart->exists) {
            return $this->handleExistingCartItem($cart, $product, $product_stock, $quantity, $carts);
        }

        $cart->notes = $request->notes;
        $cart->save();

        // Calculate price and tax, save cart data
        $price = CartUtility::get_price($product, $product_stock, $request->quantity);
        $tax = CartUtility::tax_calculation($product, $price);
        CartUtility::save_cart_data($cart, $product, $price, $tax, $quantity);

        // Retrieve updated cart items
        $carts = $this->getCarts($request);

        return $this->responseArray(1, $carts, 'addedToCart', compact('product', 'cart'));
    }

    // Removes an item from the cart
    public function removeFromCart(Request $request)
    {
        Cart::destroy($request->id);
        $carts = $this->getCarts(request());

        return array(
            'cart_count' => $carts->count(),
            'cart_view' => view('frontend.' . get_setting('homepage_select') . '.partials.cart_details', compact('carts'))->render(),
            'nav_cart_view' => view('frontend.' . get_setting('homepage_select') . '.partials.cart-items', compact('carts'))->render(),
        );
    }

    // Updates the quantity for a cart item
    public function updateQuantity(Request $request)
    {
        $cartItem = Cart::findOrFail($request->id);
        $product = Product::find($cartItem['product_id']);
        $product_stock = $product->stocks->where('variant', $cartItem['variation'])->first();

        // Check quantity limits
        if ($product_stock->qty >= $request->quantity && $request->quantity >= $product->min_qty) {
            $cartItem['quantity'] = $request->quantity;
            $cartItem['price'] = $this->calculatePrice($product, $product_stock, $request->quantity);
            $cartItem->save();
        }

        $carts = $this->getCarts(request());

        return array(
            'cart_count' => count($carts),
            'cart_view' => view('frontend.' . get_setting('homepage_select') . '.partials.cart_details', compact('carts'))->render(),
            'nav_cart_view' => view('frontend.' . get_setting('homepage_select') . '.partials.cart-items', compact('carts'))->render(),
        );
    }

    // Helper method to migrate temporary cart to user cart
    private function migrateTempCartToUser($userId, $tempUserId)
    {
        if ($tempUserId) {
            Cart::where('temp_user_id', $tempUserId)
                ->update([
                    'user_id' => $userId,
                    'temp_user_id' => null
                ]);
            Session::forget('temp_user_id');
        }
    }

    // Helper method to retrieve carts
    private function getCarts(Request $request)
    {
        if (auth()->check()) {
            return Cart::where('user_id', Auth::user()->id)->get();
        } else {
            $temp_user_id = $request->session()->get('temp_user_id');
            return $temp_user_id ? Cart::where('temp_user_id', $temp_user_id)->get() : [];
        }
    }

    // Helper method to handle existing cart item
    private function handleExistingCartItem($cart, $product, $product_stock, $quantity, $carts)
    {
        if ($product->auction_product == 1 && ($cart->product_id == $product->id)) {
            return $this->responseArray(0, $carts, 'auctionProductAlreadyAddedCart');
        }

        if ($product_stock->qty < $cart->quantity + $quantity) {
            return $this->responseArray(0, $carts, 'outOfStockCart');
        }

        $quantity = $cart->quantity + $quantity;

        return $this->updateCart($cart, $product, $product_stock, $quantity);
    }

    // Helper method to update cart data
    private function updateCart($cart, $product, $product_stock, $quantity)
    {
        $price = CartUtility::get_price($product, $product_stock, $quantity);
        $tax = CartUtility::tax_calculation($product, $price);
        CartUtility::save_cart_data($cart, $product, $price, $tax, $quantity);

        $userId = auth()->check() ? auth()->id() : null;
        $tempUserId = null;
        $carts = collect(); // Initialize as an empty collection for consistency

        // Handle guest or authenticated user cart retrieval
        if ($userId) {
            $carts = Cart::where('user_id', $userId)->get();
        } else {
            if (!session()->has('temp_user_id')) {
                $tempUserId = uniqid('guest_', true);
                session()->put('temp_user_id', $tempUserId);
            } else {
                $tempUserId = session()->get('temp_user_id');
            }
            $carts = Cart::where('temp_user_id', $tempUserId)->get();
        }

        // Return the updated cart details
        return [
            'status' => 1,
            'cart_count' => $carts->count(),
            'modal_view' => view('frontend.' . get_setting('homepage_select') . '.partials.addedToCart', compact('product', 'cart'))->render(),
            'nav_cart_view' => view('frontend.' . get_setting('homepage_select') . '.partials.cart-items', compact('carts'))->render(),
        ];
    }

    // Helper method to calculate price based on discounts
    private function calculatePrice($product, $product_stock, $quantity)
    {
        $price = $product_stock->price;

        // Discount calculation
        if ($this->isDiscountApplicable($product)) {
            if ($product->discount_type == 'percent') {
                $price -= ($price * $product->discount) / 100;
            } elseif ($product->discount_type == 'amount') {
                $price -= $product->discount;
            }
        }

        return $price;
    }

    // Helper method to check if discount is applicable
    private function isDiscountApplicable($product)
    {
        $currentDate = strtotime(date('d-m-Y H:i:s'));
        return $product->discount_start_date == null ||
            ($currentDate >= $product->discount_start_date && $currentDate <= $product->discount_end_date);
    }

    // Helper method to create a standardized response array
    private function responseArray($status, $carts, $view, $data = [])
    {
        return array_merge(
            [
                'status' => $status,
                'cart_count' => $carts->count(),
                'modal_view' => view('frontend.' . get_setting('homepage_select') . '.partials.' . $view, $data)->render(),
                'nav_cart_view' => view('frontend.' . get_setting('homepage_select') . '.partials.cart-items', compact('carts'))->render(),
            ],
            $data
        );
    }
}
