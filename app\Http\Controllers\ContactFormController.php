<?php

namespace App\Http\Controllers;

use App\Models\ContactForm;
use Illuminate\Http\Request;

class ContactFormController extends Controller
{
    // List all contacts
    public function index()
    {
        $contacts = ContactForm::all();
        return view('backend.contact-form.index', compact('contacts'));
    }

    // Show form for creating a new contact
    public function create()
    {
        return view('backend.contact-form.create');
    }

    // Store a newly created contact
    public function store(Request $request)
    {
        $validated = $request->validate([
            'firstname' => 'required|string|max:255',
            'lastname' => 'required|string|max:255',
            'phone' => 'required|string|max:15',
            'email' => 'required|email|max:255',
            'message' => 'required|string',
        ]);

        ContactForm::create($validated);

        return redirect()->route('contact-form.index')->with('success', 'Contact saved successfully.');
    }

    // Display a specific contact
    public function show(ContactForm $contact)
    {
        return view('contact-form.show', compact('contact'));
    }

    // Show form for editing a contact
    public function edit(ContactForm $contact)
    {
        return view('contact-form.edit', compact('contact'));
    }

    // Update a contact
    public function update(Request $request, ContactForm $contact)
    {
        $validated = $request->validate([
            'firstname' => 'required|string|max:255',
            'lastname' => 'required|string|max:255',
            'phone' => 'required|string|max:15',
            'email' => 'required|email|max:255',
            'message' => 'required|string',
        ]);

        $contact->update($validated);

        return redirect()->route('contact-form.index')->with('success', 'Contact updated successfully.');
    }

    // Delete a contact
    public function destroy(Request $request)
    {
        ContactForm::findOrFail($request->id)->delete();

        return redirect()->route('contact-form.index')->with('success', 'Contact deleted successfully.');
    }
}
