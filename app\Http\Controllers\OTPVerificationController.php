<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Utility\SendSMSUtility;

class OTPVerificationController extends Controller
{
    /**
     * Show the phone verification form
     */
    public function verification()
    {
        $user = Auth::user();
        
        if (!$user) {
            return redirect()->route('user.login');
        }

        // If user already verified, redirect to home
        if ($user->phone_verified_at) {
            return redirect()->route('home');
        }

        return view('otp_systems.frontend.auth.'.get_setting('authentication_layout_select').'.phone_verification', compact('user'));
    }

    /**
     * Verify the phone OTP
     */
    public function verify_phone(Request $request)
    {
        $request->validate([
            'verification_code' => 'required|string|size:6'
        ]);

        $user = Auth::user();
        
        if (!$user) {
            flash(translate('Please login first'))->error();
            return redirect()->route('user.login');
        }

        // Check if the verification code matches
        if ($user->verification_code == $request->verification_code) {
            // Mark phone as verified
            $user->phone_verified_at = now();
            $user->verification_code = null;
            $user->save();

            // Offer welcome coupon for customers only
            if ($user->user_type == 'customer') {
                offerUserWelcomeCoupon();
            }

            flash(translate('Phone verification successful!'))->success();

            // Redirect based on user type
            if ($user->user_type == 'seller') {
                return redirect()->route('seller.shop.index');
            } else {
                return redirect()->route('home');
            }
        } else {
            flash(translate('Invalid verification code'))->error();
            return back();
        }
    }

    /**
     * Resend verification code
     */
    public function resend_verificcation_code()
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['status' => 'error', 'message' => 'Please login first']);
        }

        if ($user->phone_verified_at) {
            return response()->json(['status' => 'error', 'message' => 'Phone already verified']);
        }

        // Generate new verification code
        $user->verification_code = rand(100000, 999999);
        $user->save();

        // Send OTP via SMS and WhatsApp
        SendSMSUtility::sendOtpViaBoth($user->phone, $user->verification_code, $user->country_code);

        return response()->json(['status' => 'success', 'message' => translate('Verification code sent successfully')]);
    }

    /**
     * Send verification code to user (used by other controllers)
     */
    public function send_code($user)
    {
        if (!$user->verification_code) {
            $user->verification_code = rand(100000, 999999);
            $user->save();
        }

        // Send OTP via SMS and WhatsApp
        SendSMSUtility::sendOtpViaBoth($user->phone, $user->verification_code, $user->country_code);
    }

    /**
     * Show reset password form (for forgot password flow)
     */
    public function show_reset_password_form()
    {
        return view('otp_systems.frontend.auth.'.get_setting('authentication_layout_select').'.reset_with_phone');
    }

    /**
     * Reset password with verification code
     */
    public function reset_password_with_code(Request $request)
    {
        $request->validate([
            'verification_code' => 'required|string|size:6',
            'password' => 'required|string|min:6|confirmed'
        ]);

        $user = User::where('verification_code', $request->verification_code)->first();

        if (!$user) {
            flash(translate('Invalid verification code'))->error();
            return back();
        }

        // Update password and clear verification code
        $user->password = bcrypt($request->password);
        $user->verification_code = null;
        $user->save();

        flash(translate('Password reset successful!'))->success();
        return redirect()->route('user.login');
    }
}
