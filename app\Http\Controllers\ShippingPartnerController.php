<?php

namespace App\Http\Controllers;

use App\Models\ShippingPartner;
use App\Models\ShippingConfiguration;
use Illuminate\Http\Request;

class ShippingPartnerController extends Controller
{
    public function __construct() {
        // Staff Permission Check
        $this->middleware(['permission:shipping_configuration'])->only('index','edit','update');
    }
    
    /**
     * Display a listing of the shipping partners.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $shippingPartners = ShippingPartner::all();
        return view('backend.setup_configurations.shipping_partners.index', compact('shippingPartners'));
    }
    
    /**
     * Show the form for editing the specified shipping partner.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $shippingPartner = ShippingPartner::findOrFail($id);
        $configuration = ShippingConfiguration::where('shipping_partner_id', $id)->first();
        
        if (!$configuration) {
            $configuration = new ShippingConfiguration();
            $configuration->shipping_partner_id = $id;
            $configuration->save();
        }
        
        return view('backend.setup_configurations.shipping_partners.edit', compact('shippingPartner', 'configuration'));
    }
    
    /**
     * Update the specified shipping partner in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $shippingPartner = ShippingPartner::findOrFail($id);

        $shippingPartner->api_key = $request->api_key;
        $shippingPartner->api_secret = $request->api_secret;

        // Set predefined API URLs for specific shipping partners
        if ($shippingPartner->code == 'shiprocket') {
            $shippingPartner->api_url = 'https://apiv2.shiprocket.in/v1/external';
        } elseif ($shippingPartner->code == 'delhivery') {
            $shippingPartner->api_url = 'https://track.delhivery.com/api';
        } elseif ($shippingPartner->code == 'ecom_express') {
            $shippingPartner->api_url = 'https://api.ecomexpress.in';
        } else {
            $shippingPartner->api_url = $request->api_url;
        }

        $shippingPartner->is_active = $request->is_active ? 1 : 0;
        $shippingPartner->save();
        
        $configuration = ShippingConfiguration::where('shipping_partner_id', $id)->first();
        if (!$configuration) {
            $configuration = new ShippingConfiguration();
            $configuration->shipping_partner_id = $id;
        }
        
        $configuration->is_active = $request->is_active ? 1 : 0;
        $configuration->use_shipping_rate_calculator = $request->use_shipping_rate_calculator ? 1 : 0;

        // Validate and format the fulfillment address JSON
        if ($request->cloudmart_fulfillment_address) {
            $addressData = json_decode($request->cloudmart_fulfillment_address, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                flash(translate('Invalid JSON format for fulfillment address'))->error();
                return redirect()->back()->withInput();
            }

            // Validate required fields
            $requiredFields = ['name', 'address', 'city', 'state', 'country', 'postal_code', 'phone'];
            $missingFields = [];

            foreach ($requiredFields as $field) {
                if (empty($addressData[$field])) {
                    $missingFields[] = $field;
                }
            }

            if (!empty($missingFields)) {
                flash(translate('Missing required fields in fulfillment address: ' . implode(', ', $missingFields)))->error();
                return redirect()->back()->withInput();
            }

            // Store properly formatted JSON
            $configuration->cloudmart_fulfillment_address = json_encode($addressData);
        }

        $configuration->save();
        
        flash(translate('Shipping partner configuration has been updated successfully'))->success();
        return redirect()->route('shipping_partners.index');
    }
    
    /**
     * Update the status of the specified shipping partner.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateStatus(Request $request)
    {
        $shippingPartner = ShippingPartner::findOrFail($request->id);
        $shippingPartner->is_active = $request->status;
        
        if ($shippingPartner->save()) {
            $configuration = ShippingConfiguration::where('shipping_partner_id', $request->id)->first();
            if ($configuration) {
                $configuration->is_active = $request->status;
                $configuration->save();
            }
            
            return 1;
        }
        
        return 0;
    }

    /**
     * Test Shiprocket connection
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function testShiprocket($id)
    {
        $shippingPartner = ShippingPartner::findOrFail($id);

        if ($shippingPartner->code !== 'shiprocket') {
            return response()->json(['error' => 'This test is only for Shiprocket'], 400);
        }

        try {
            $shippingService = ShippingServiceFactory::getService($shippingPartner);

            // Test authentication
            $authResult = $shippingService->testAuthentication();
            if (!$authResult['success']) {
                return response()->json($authResult);
            }

            // Test pickup location if fulfillment address is configured
            $config = ShippingConfiguration::where('shipping_partner_id', $id)->first();
            if ($config && $config->cloudmart_fulfillment_address) {
                $address = json_decode($config->cloudmart_fulfillment_address, true);
                if ($address) {
                    $pickupResult = $shippingService->testPickupLocation($address);
                    return response()->json([
                        'auth' => $authResult,
                        'pickup' => $pickupResult
                    ]);
                }
            }

            return response()->json([
                'auth' => $authResult,
                'pickup' => ['success' => false, 'message' => 'No fulfillment address configured']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test shipping partner connection
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function testConnection($id)
    {
        $shippingPartner = ShippingPartner::findOrFail($id);

        try {
            $shippingService = ShippingServiceFactory::getService($shippingPartner);

            // Test credentials validation
            if (method_exists($shippingService, 'validateCredentials')) {
                $result = $shippingService->validateCredentials();
                return response()->json($result);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Test method not available for ' . $shippingPartner->name
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error testing connection: ' . $e->getMessage()
            ], 500);
        }
    }
}
