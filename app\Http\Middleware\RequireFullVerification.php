<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RequireFullVerification
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (Auth::check()) {
            $user = Auth::user();
            
            // Skip verification check for admin and staff
            if (in_array($user->user_type, ['admin', 'staff'])) {
                return $next($request);
            }
            
            // Check if user is fully verified (both email and phone)
            if (!$user->isFullyVerified()) {
                // Allow access to verification routes
                $allowedRoutes = [
                    'verification.notice',
                    'verification.resend', 
                    'verification.verify',
                    'email.verification.confirmation',
                    'phone.verification',
                    'user.verification',
                    'user.verification.send',
                    'user.verification.verify',
                    'user.verification.status',
                    'logout'
                ];
                
                if (!in_array($request->route()->getName(), $allowedRoutes)) {
                    // Redirect to verification page
                    return redirect()->route('user.verification');
                }
            }
        }
        
        return $next($request);
    }
}
