<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ShippingPartner extends Model
{
    protected $fillable = [
        'name', 
        'code', 
        'description', 
        'api_key', 
        'api_secret', 
        'api_url', 
        'is_active'
    ];

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function configurations()
    {
        return $this->hasMany(ShippingConfiguration::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', 1);
    }
}
