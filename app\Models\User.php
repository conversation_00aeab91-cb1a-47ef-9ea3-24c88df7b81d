<?php

namespace App\Models;

use Illuminate\Notifications\Notifiable;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use App\Models\Cart;
use App\Notifications\EmailVerificationNotification;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements MustVerifyEmail
{
    use Notifiable, HasApiTokens, HasRoles;

    public function sendEmailVerificationNotification()
    {
        $this->notify(new EmailVerificationNotification());
    }

    /**
     * Check if phone is verified
     */
    public function hasVerifiedPhone()
    {
        return !is_null($this->phone_verified_at);
    }

    /**
     * Check if email is verified
     */
    public function hasVerifiedEmail()
    {
        return !is_null($this->email_verified_at);
    }

    /**
     * Check if both email and phone are verified
     */
    public function isFullyVerified()
    {
        return $this->hasVerifiedEmail() && $this->hasVerifiedPhone();
    }

    /**
     * Get full phone number with country code
     */
    public function getFullPhoneAttribute()
    {
        if ($this->phone && $this->country_code) {
            return $this->country_code . $this->phone;
        }
        return $this->phone;
    }

    /**
     * Get phone number without country code
     */
    public function getPhoneWithoutCountryCodeAttribute()
    {
        if ($this->phone && $this->country_code) {
            return str_replace($this->country_code, '', $this->phone);
        }
        return $this->phone;
    }

    /**
     * Check if user can request phone verification
     */
    public function canRequestPhoneVerification()
    {
        if (!$this->last_phone_verification_attempt) {
            return true;
        }

        // Allow new verification after 2 minutes
        return $this->last_phone_verification_attempt->diffInMinutes(now()) >= 2;
    }

    /**
     * Check if user can request email verification
     */
    public function canRequestEmailVerification()
    {
        if (!$this->last_email_verification_attempt) {
            return true;
        }

        // Allow new verification after 2 minutes
        return $this->last_email_verification_attempt->diffInMinutes(now()) >= 2;
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'email', 'password', 'address', 'city', 'postal_code', 'phone', 'country_code', 'country',
        'provider_id', 'email_verified_at', 'verification_code', 'phone_verified_at', 'phone_verification_code',
        'phone_verification_sent_at', 'email_verification_code', 'email_verification_sent_at',
        'is_phone_verified', 'is_email_verified', 'phone_verification_attempts', 'email_verification_attempts',
        'last_phone_verification_attempt', 'last_email_verification_attempt'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    public function wishlists()
    {
        return $this->hasMany(Wishlist::class);
    }

    public function customer()
    {
        return $this->hasOne(Customer::class);
    }

    public function affiliate_user()
    {
        return $this->hasOne(AffiliateUser::class);
    }

    public function affiliate_withdraw_request()
    {
        return $this->hasMany(AffiliateWithdrawRequest::class);
    }

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function shop()
    {
        return $this->hasOne(Shop::class);
    }
    public function seller()
    {
        return $this->hasOne(Seller::class);
    }


    public function staff()
    {
        return $this->hasOne(Staff::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function seller_orders()
    {
        return $this->hasMany(Order::class, "seller_id");
    }
    public function seller_sales()
    {
        return $this->hasMany(OrderDetail::class, "seller_id");
    }

    public function wallets()
    {
        return $this->hasMany(Wallet::class)->orderBy('created_at', 'desc');
    }

    public function club_point()
    {
        return $this->hasOne(ClubPoint::class);
    }

    public function customer_package()
    {
        return $this->belongsTo(CustomerPackage::class);
    }

    public function customer_package_payments()
    {
        return $this->hasMany(CustomerPackagePayment::class);
    }

    public function customer_products()
    {
        return $this->hasMany(CustomerProduct::class);
    }

    public function seller_package_payments()
    {
        return $this->hasMany(SellerPackagePayment::class);
    }

    public function carts()
    {
        return $this->hasMany(Cart::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    public function addresses()
    {
        return $this->hasMany(Address::class);
    }

    public function affiliate_log()
    {
        return $this->hasMany(AffiliateLog::class);
    }

    public function product_bids()
    {
        return $this->hasMany(AuctionProductBid::class);
    }

    public function product_queries(){
        return $this->hasMany(ProductQuery::class,'customer_id');
    }

    public function uploads(){
        return $this->hasMany(Upload::class);
    }

    public function userCoupon(){
        return $this->hasOne(UserCoupon::class);
    }
}
