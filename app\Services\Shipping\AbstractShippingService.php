<?php

namespace App\Services\Shipping;

use App\Models\Order;
use App\Models\Product;
use App\Models\ShippingPartner;
use Illuminate\Support\Facades\Http;

abstract class AbstractShippingService implements ShippingServiceInterface
{
    protected $shippingPartner;
    protected $apiKey;
    protected $apiSecret;
    protected $apiUrl;
    
    public function __construct(ShippingPartner $shippingPartner)
    {
        $this->shippingPartner = $shippingPartner;
        $this->apiKey = $shippingPartner->api_key;
        $this->apiSecret = $shippingPartner->api_secret;
        $this->apiUrl = $shippingPartner->api_url;
    }
    
    /**
     * Get the from address based on product fulfillment type
     *
     * @param Product $product
     * @return array
     */
    protected function getFromAddress(Product $product): array
    {
        if ($product->fulfillment_type == 'cloudmart') {
            // Get Cloud Mart's fulfillment center address
            $config = \App\Models\ShippingConfiguration::where('shipping_partner_id', $this->shippingPartner->id)
                ->first();
                
            if ($config && $config->cloudmart_fulfillment_address) {
                return json_decode($config->cloudmart_fulfillment_address, true);
            }
            
            // Default Cloud Mart address if not configured
            return [
                'name' => 'Cloud Mart Fulfillment Center',
                'address' => 'Cloud Mart Address',
                'city' => 'Cloud Mart City',
                'state' => 'Cloud Mart State',
                'country' => 'Cloud Mart Country',
                'postal_code' => 'Cloud Mart Postal Code',
                'phone' => 'Cloud Mart Phone'
            ];
        } else {
            // Get vendor's address with failsafe fallback to CloudMart address
            $seller = $product->user;
            $shop = \App\Models\Shop::where('user_id', $seller->id)->first();

            // Try to get vendor's default address first
            $vendorAddress = \App\Models\Address::where('user_id', $seller->id)
                ->where('set_default', 1)
                ->first();

            if (!$vendorAddress) {
                $vendorAddress = \App\Models\Address::where('user_id', $seller->id)->first();
            }

            // Check if vendor has a valid address
            $hasValidVendorAddress = $vendorAddress &&
                !empty($vendorAddress->address) &&
                !empty($vendorAddress->postal_code) &&
                !empty($vendorAddress->phone);

            if ($hasValidVendorAddress) {
                return [
                    'name' => $shop ? $shop->name : $seller->name,
                    'address' => $vendorAddress->address,
                    'city' => $vendorAddress->city->name ?? 'Bengaluru',
                    'state' => $vendorAddress->state->name ?? 'Karnataka',
                    'country' => $vendorAddress->country->name ?? 'India',
                    'postal_code' => $vendorAddress->postal_code,
                    'phone' => $vendorAddress->phone
                ];
            } else {
                // Failsafe: Use CloudMart fulfillment address as default for vendor products too
                $config = \App\Models\ShippingConfiguration::where('shipping_partner_id', $this->shippingPartner->id)
                    ->first();

                if ($config && $config->cloudmart_fulfillment_address) {
                    return json_decode($config->cloudmart_fulfillment_address, true);
                }

                // Throw exception if CloudMart address not configured
                throw new \Exception('CloudMart fulfillment address not configured. Please configure it in Shipping Partners settings.');
            }
        }
    }
    
    /**
     * Make an API request to the shipping partner
     *
     * @param string $endpoint
     * @param array $data
     * @param string $method
     * @return array
     */
    protected function makeApiRequest(string $endpoint, array $data = [], string $method = 'POST'): array
    {
        $url = $this->apiUrl . '/' . $endpoint;
        
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json'
        ]);
        
        if ($method == 'GET') {
            $response = $response->get($url, $data);
        } else {
            $response = $response->post($url, $data);
        }
        
        return $response->json();
    }
}
