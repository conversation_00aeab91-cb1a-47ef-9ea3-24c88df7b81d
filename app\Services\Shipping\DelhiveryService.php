<?php

namespace App\Services\Shipping;

use App\Models\Order;
use App\Models\ShippingPartner;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DelhiveryService extends AbstractShippingService
{
    protected $baseUrl = 'https://track.delhivery.com/api';

    public function __construct(ShippingPartner $shippingPartner = null)
    {
        if (!$shippingPartner) {
            $shippingPartner = ShippingPartner::where('code', 'delhivery')->first();
        }

        parent::__construct($shippingPartner);

        // Validate API credentials
        if (empty($this->apiKey)) {
            Log::error('Delhivery API Token is not configured');
        }
    }

    /**
     * Calculate shipping rate for an order
     *
     * @param Order $order
     * @param array $fromAddress
     * @param array $toAddress
     * @return float
     */
    public function calculateRate(Order $order, array $fromAddress, array $toAddress): float
    {
        if (empty($this->apiKey)) {
            Log::warning('Delhivery API Token not configured, using fallback rate');
            return 120.00;
        }

        // Validate required address fields
        if (empty($fromAddress['postal_code']) || empty($toAddress['postal_code'])) {
            Log::warning('Missing postal codes for Delhivery rate calculation');
            return 120.00;
        }

        $weight = 0;

        // Calculate total weight
        foreach ($order->orderDetails as $orderDetail) {
            $product = $orderDetail->product;
            $weight += ($product->weight ?? 0.5) * $orderDetail->quantity;
        }

        // Minimum weight of 0.5 kg
        $weight = max($weight, 0.5);

        try {
            Log::info('Delhivery Rate Calculation Request', [
                'from_pin' => $fromAddress['postal_code'],
                'to_pin' => $toAddress['postal_code'],
                'weight' => $weight,
                'order_code' => $order->code
            ]);

            $response = Http::withHeaders([
                'Authorization' => 'Token ' . $this->apiKey,
                'Content-Type' => 'application/json'
            ])->get($this->baseUrl . '/kinko/v1/invoice/charges/.json', [
                'md' => 'S', // Mode: Surface
                'ss' => 'Delivered', // Service type
                'o_pin' => $fromAddress['postal_code'],
                'd_pin' => $toAddress['postal_code'],
                'cgm' => $weight * 1000, // Weight in grams
                'pt' => $order->payment_type == 'cash_on_delivery' ? 'COD' : 'Pre-paid',
                'cod' => $order->payment_type == 'cash_on_delivery' ? $order->grand_total : 0
            ]);

            Log::info('Delhivery Rate Calculation Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data[0]['total_amount'])) {
                    $rate = (float) $data[0]['total_amount'];
                    Log::info("Delhivery rate calculated: ₹{$rate} for order {$order->code}");
                    return $rate;
                }
            } else {
                Log::error('Delhivery Rate API Error', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Delhivery Rate Calculation Error: ' . $e->getMessage(), [
                'order_code' => $order->code,
                'from_pin' => $fromAddress['postal_code'] ?? 'N/A',
                'to_pin' => $toAddress['postal_code'] ?? 'N/A'
            ]);
        }

        return 120.00; // Fallback rate
    }

    /**
     * Create a shipping order with Delhivery
     *
     * @param Order $order
     * @param array $fromAddress
     * @param array $toAddress
     * @return array
     */
    public function createShippingOrder(Order $order, array $fromAddress, array $toAddress): array
    {
        if (empty($this->apiKey)) {
            return [
                'success' => false,
                'message' => 'Delhivery API Token is not configured. Please configure it in shipping partner settings.'
            ];
        }

        // Validate required fields
        $requiredFromFields = ['name', 'address', 'postal_code', 'city', 'state', 'country', 'phone'];
        $requiredToFields = ['address', 'postal_code', 'city', 'state', 'country', 'phone'];

        foreach ($requiredFromFields as $field) {
            if (empty($fromAddress[$field])) {
                return [
                    'success' => false,
                    'message' => "Missing required from address field: {$field}"
                ];
            }
        }

        foreach ($requiredToFields as $field) {
            if (empty($toAddress[$field])) {
                return [
                    'success' => false,
                    'message' => "Missing required to address field: {$field}"
                ];
            }
        }

        $weight = 0;

        // Calculate total weight
        foreach ($order->orderDetails as $orderDetail) {
            $product = $orderDetail->product;
            $weight += ($product->weight ?? 0.5) * $orderDetail->quantity;
        }

        // Minimum weight of 0.5 kg
        $weight = max($weight, 0.5);

        // Clean phone numbers (remove spaces and special characters)
        $fromPhone = preg_replace('/[^0-9]/', '', $fromAddress['phone']);
        $toPhone = preg_replace('/[^0-9]/', '', $toAddress['phone']);

        $shipmentData = [
            'name' => trim($order->user->name),
            'add' => trim($toAddress['address']),
            'pin' => trim($toAddress['postal_code']),
            'city' => trim($toAddress['city']),
            'state' => trim($toAddress['state']),
            'country' => trim($toAddress['country']),
            'phone' => $toPhone,
            'order' => $order->code,
            'payment_mode' => $order->payment_type == 'cash_on_delivery' ? 'COD' : 'Prepaid',
            'return_pin' => trim($fromAddress['postal_code']),
            'return_city' => trim($fromAddress['city']),
            'return_phone' => $fromPhone,
            'return_add' => trim($fromAddress['address']),
            'return_state' => trim($fromAddress['state']),
            'return_country' => trim($fromAddress['country']),
            'products_desc' => 'E-commerce Products',
            'hsn_code' => '',
            'cod_amount' => $order->payment_type == 'cash_on_delivery' ? $order->grand_total : 0,
            'order_date' => date('Y-m-d H:i:s', $order->date),
            'total_amount' => $order->grand_total,
            'seller_add' => trim($fromAddress['address']),
            'seller_name' => trim($fromAddress['name']),
            'seller_inv' => $order->code,
            'quantity' => $order->orderDetails->sum('quantity'),
            'waybill' => '',
            'shipment_width' => 10,
            'shipment_height' => 5,
            'weight' => $weight,
            'seller_gst_tin' => '',
            'shipping_mode' => 'Surface',
            'address_type' => 'home'
        ];

        $data = [
            'format' => 'json',
            'data' => json_encode([
                'shipments' => [$shipmentData]
            ])
        ];

        try {
            Log::info('Delhivery Order Creation Request', [
                'order_code' => $order->code,
                'url' => $this->baseUrl . '/cmu/create.json',
                'shipment_data' => $shipmentData
            ]);

            $response = Http::withHeaders([
                'Authorization' => 'Token ' . $this->apiKey,
                'Content-Type' => 'application/json'
            ])->post($this->baseUrl . '/cmu/create.json', $data);

            Log::info('Delhivery Order Creation Response', [
                'order_code' => $order->code,
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            if ($response->successful()) {
                $responseData = $response->json();

                if (isset($responseData['packages']) && !empty($responseData['packages'])) {
                    $package = $responseData['packages'][0];

                    // Check if package creation was successful
                    if (isset($package['status']) && $package['status'] === 'Success') {
                        return [
                            'success' => true,
                            'tracking_id' => $package['waybill'] ?? null,
                            'shipment_id' => $package['refnum'] ?? null,
                            'response' => json_encode($responseData)
                        ];
                    } else {
                        return [
                            'success' => false,
                            'message' => 'Delhivery order creation failed: ' . ($package['remarks'] ?? 'Unknown error')
                        ];
                    }
                } else {
                    return [
                        'success' => false,
                        'message' => 'Invalid response from Delhivery API: ' . $response->body()
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'message' => 'Delhivery API Error (HTTP ' . $response->status() . '): ' . $response->body()
                ];
            }

        } catch (\Exception $e) {
            Log::error('Delhivery Order Creation Error: ' . $e->getMessage(), [
                'order_code' => $order->code,
                'exception' => $e->getTraceAsString()
            ]);
            return [
                'success' => false,
                'message' => 'Error creating order with Delhivery: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Track a shipment with Delhivery
     *
     * @param string $trackingId
     * @return array
     */
    public function trackShipment(string $trackingId): array
    {
        if (empty($this->apiKey)) {
            return [
                'tracking_id' => $trackingId,
                'status' => 'error',
                'message' => 'Delhivery API Token is not configured'
            ];
        }

        if (empty($trackingId)) {
            return [
                'tracking_id' => $trackingId,
                'status' => 'error',
                'message' => 'Tracking ID is required'
            ];
        }

        try {
            Log::info('Delhivery Tracking Request', [
                'tracking_id' => $trackingId,
                'url' => $this->baseUrl . '/v1/packages/json/'
            ]);

            $response = Http::withHeaders([
                'Authorization' => 'Token ' . $this->apiKey,
                'Content-Type' => 'application/json'
            ])->get($this->baseUrl . '/v1/packages/json/', [
                'waybill' => $trackingId
            ]);

            Log::info('Delhivery Tracking Response', [
                'tracking_id' => $trackingId,
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['ShipmentData']) && !empty($data['ShipmentData'])) {
                    $shipmentData = $data['ShipmentData'][0]['Shipment'];
                    $status = $shipmentData['Status'];

                    return [
                        'tracking_id' => $trackingId,
                        'status' => $status['Status'] ?? 'unknown',
                        'current_location' => $status['Instructions'] ?? 'Unknown',
                        'expected_delivery' => $shipmentData['ExpectedDeliveryDate'] ?? null,
                        'tracking_url' => 'https://www.delhivery.com/track/package/' . $trackingId,
                        'tracking_data' => $shipmentData
                    ];
                } else {
                    return [
                        'tracking_id' => $trackingId,
                        'status' => 'not_found',
                        'message' => 'No tracking data found for this waybill'
                    ];
                }
            } else {
                Log::error('Delhivery Tracking API Error', [
                    'tracking_id' => $trackingId,
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);

                return [
                    'tracking_id' => $trackingId,
                    'status' => 'error',
                    'message' => 'API Error (HTTP ' . $response->status() . '): ' . $response->body()
                ];
            }

        } catch (\Exception $e) {
            Log::error('Delhivery Tracking Error: ' . $e->getMessage(), [
                'tracking_id' => $trackingId,
                'exception' => $e->getTraceAsString()
            ]);
            return [
                'tracking_id' => $trackingId,
                'status' => 'error',
                'message' => 'Error tracking shipment: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Validate Delhivery API credentials
     *
     * @return array
     */
    public function validateCredentials(): array
    {
        if (empty($this->apiKey)) {
            return [
                'success' => false,
                'message' => 'API Token is required'
            ];
        }

        try {
            // Test API with a simple request
            $response = Http::withHeaders([
                'Authorization' => 'Token ' . $this->apiKey,
                'Content-Type' => 'application/json'
            ])->get($this->baseUrl . '/kinko/v1/invoice/charges/.json', [
                'md' => 'S',
                'ss' => 'Delivered',
                'o_pin' => '110001',
                'd_pin' => '400001',
                'cgm' => 500,
                'pt' => 'Pre-paid',
                'cod' => 0
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'message' => 'Delhivery API credentials are valid'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Invalid API credentials or API error: ' . $response->body()
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error validating credentials: ' . $e->getMessage()
            ];
        }
    }
}
