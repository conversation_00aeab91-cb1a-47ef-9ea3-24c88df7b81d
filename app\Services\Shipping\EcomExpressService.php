<?php

namespace App\Services\Shipping;

use App\Models\Order;
use App\Models\ShippingPartner;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class EcomExpressService extends AbstractShippingService
{
    protected $baseUrl = 'https://api.ecomexpress.in';

    public function __construct(ShippingPartner $shippingPartner = null)
    {
        if (!$shippingPartner) {
            $shippingPartner = ShippingPartner::where('code', 'ecom_express')->first();
        }

        parent::__construct($shippingPartner);
    }

    /**
     * Calculate shipping rate for an order
     *
     * @param Order $order
     * @param array $fromAddress
     * @param array $toAddress
     * @return float
     */
    public function calculateRate(Order $order, array $fromAddress, array $toAddress): float
    {
        $weight = 0;

        // Calculate total weight
        foreach ($order->orderDetails as $orderDetail) {
            $product = $orderDetail->product;
            $weight += ($product->weight ?? 0.5) * $orderDetail->quantity;
        }

        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json'
            ])->post($this->baseUrl . '/services/shipment/products/v2/fetch_awb_charges', [
                'username' => $this->apiKey,
                'password' => $this->apiSecret,
                'origin_pincode' => $fromAddress['postal_code'],
                'destination_pincode' => $toAddress['postal_code'],
                'length' => 10,
                'breadth' => 10,
                'height' => 5,
                'weight' => $weight,
                'payment_mode' => $order->payment_type == 'cash_on_delivery' ? 'COD' : 'PPD',
                'product_type' => 'Normal Delivery'
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['charge_details']['total_charge'])) {
                    return (float) $data['charge_details']['total_charge'];
                }
            }
        } catch (\Exception $e) {
            Log::error('Ecom Express Rate Calculation Error: ' . $e->getMessage());
        }

        return 130.00; // Fallback rate
    }
    
    /**
     * Create a shipping order with Ecom Express
     *
     * @param Order $order
     * @param array $fromAddress
     * @param array $toAddress
     * @return array
     */
    public function createShippingOrder(Order $order, array $fromAddress, array $toAddress): array
    {
        $weight = 0;

        // Calculate total weight
        foreach ($order->orderDetails as $orderDetail) {
            $product = $orderDetail->product;
            $weight += ($product->weight ?? 0.5) * $orderDetail->quantity;
        }

        $data = [
            'username' => $this->apiKey,
            'password' => $this->apiSecret,
            'json_input' => json_encode([[
                'AWB_NUMBER' => '',
                'ORDER_NUMBER' => $order->code,
                'PRODUCT' => 'Normal Delivery',
                'CONSIGNEE' => $order->user->name,
                'CONSIGNEE_ADDRESS1' => $toAddress['address'],
                'CONSIGNEE_ADDRESS2' => '',
                'CONSIGNEE_ADDRESS3' => '',
                'DESTINATION_CITY' => $toAddress['city'],
                'PINCODE' => $toAddress['postal_code'],
                'STATE' => $toAddress['state'],
                'MOBILE' => $toAddress['phone'],
                'TELEPHONE' => '',
                'ITEM_DESCRIPTION' => 'E-commerce Products',
                'PIECES' => $order->orderDetails->sum('quantity'),
                'COLLECTABLE_VALUE' => $order->payment_type == 'cash_on_delivery' ? $order->grand_total : 0,
                'DECLARED_VALUE' => $order->grand_total,
                'ACTUAL_WEIGHT' => $weight,
                'VOLUMETRIC_WEIGHT' => $weight,
                'LENGTH' => 10,
                'BREADTH' => 10,
                'HEIGHT' => 5,
                'PICKUP_NAME' => $fromAddress['name'],
                'PICKUP_ADDRESS_LINE1' => $fromAddress['address'],
                'PICKUP_ADDRESS_LINE2' => '',
                'PICKUP_CITY' => $fromAddress['city'],
                'PICKUP_PINCODE' => $fromAddress['postal_code'],
                'PICKUP_MOBILE' => $fromAddress['phone'],
                'PICKUP_PHONE' => '',
                'RETURN_NAME' => $fromAddress['name'],
                'RETURN_ADDRESS_LINE1' => $fromAddress['address'],
                'RETURN_ADDRESS_LINE2' => '',
                'RETURN_CITY' => $fromAddress['city'],
                'RETURN_PINCODE' => $fromAddress['postal_code'],
                'RETURN_MOBILE' => $fromAddress['phone'],
                'RETURN_PHONE' => '',
                'DG_SHIPMENT' => 'false',
                'ADDITIONAL_INFORMATION' => [
                    'GST_TAX_CGSTN' => 0,
                    'GST_TAX_IGSTN' => 0,
                    'GST_TAX_SGSTN' => 0,
                    'SELLER_GSTIN' => '',
                    'INVOICE_NUMBER' => $order->code,
                    'INVOICE_DATE' => date('Y-m-d', $order->date),
                    'E_WAYBILL' => ''
                ]
            ]])
        ];

        try {
            $response = Http::asForm()->post($this->baseUrl . '/services/shipment/products/v2/fetch_awb', $data);

            if ($response->successful()) {
                $responseData = $response->json();

                if (isset($responseData[0]['success']) && $responseData[0]['success'] === true) {
                    return [
                        'success' => true,
                        'tracking_id' => $responseData[0]['awb'] ?? null,
                        'shipment_id' => $responseData[0]['order_number'] ?? null,
                        'response' => json_encode($responseData)
                    ];
                }
            }

            Log::error('Ecom Express Order Creation Failed: ' . $response->body());
            return [
                'success' => false,
                'message' => 'Failed to create order with Ecom Express: ' . $response->body()
            ];

        } catch (\Exception $e) {
            Log::error('Ecom Express Order Creation Error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error creating order with Ecom Express: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Track a shipment with Ecom Express
     *
     * @param string $trackingId
     * @return array
     */
    public function trackShipment(string $trackingId): array
    {
        try {
            $response = Http::asForm()->post($this->baseUrl . '/track_me/api/mawbd', [
                'username' => $this->apiKey,
                'password' => $this->apiSecret,
                'awb' => $trackingId
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data[0]) && $data[0]['field_1'] === 'TRUE') {
                    $trackingData = $data[0];

                    return [
                        'tracking_id' => $trackingId,
                        'status' => $trackingData['field_13'] ?? 'unknown',
                        'current_location' => $trackingData['field_17'] ?? 'Unknown',
                        'expected_delivery' => $trackingData['field_18'] ?? null,
                        'tracking_url' => 'https://ecomexpress.in/tracking/?awb_no=' . $trackingId,
                        'tracking_data' => $trackingData
                    ];
                }
            }

            return [
                'tracking_id' => $trackingId,
                'status' => 'unknown',
                'message' => 'No tracking data found'
            ];

        } catch (\Exception $e) {
            Log::error('Ecom Express Tracking Error: ' . $e->getMessage());
            return [
                'tracking_id' => $trackingId,
                'status' => 'error',
                'message' => 'Error tracking shipment: ' . $e->getMessage()
            ];
        }
    }
}
