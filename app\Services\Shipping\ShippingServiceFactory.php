<?php

namespace App\Services\Shipping;

use App\Models\ShippingPartner;
use Exception;

class ShippingServiceFactory
{
    /**
     * Get the appropriate shipping service based on the shipping partner
     *
     * @param ShippingPartner|int|string $shippingPartner
     * @return ShippingServiceInterface
     * @throws Exception
     */
    public static function getService($shippingPartner): ShippingServiceInterface
    {
        // If $shippingPartner is an ID or code, get the ShippingPartner model
        if (is_numeric($shippingPartner)) {
            $shippingPartner = ShippingPartner::findOrFail($shippingPartner);
        } elseif (is_string($shippingPartner)) {
            $shippingPartner = ShippingPartner::where('code', $shippingPartner)->firstOrFail();
        }
        
        // Return the appropriate service based on the shipping partner code
        switch ($shippingPartner->code) {
            case 'shiprocket':
                return new ShiprocketService($shippingPartner);
            case 'delhivery':
                return new DelhiveryService($shippingPartner);
            case 'ecom_express':
                return new EcomExpressService($shippingPartner);
            default:
                throw new Exception("Unsupported shipping partner: {$shippingPartner->name}");
        }
    }
}
