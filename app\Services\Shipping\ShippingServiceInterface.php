<?php

namespace App\Services\Shipping;

use App\Models\Order;

interface ShippingServiceInterface
{
    /**
     * Calculate shipping rate for an order
     *
     * @param Order $order
     * @param array $fromAddress
     * @param array $toAddress
     * @return float
     */
    public function calculateRate(Order $order, array $fromAddress, array $toAddress): float;
    
    /**
     * Create a shipping order with the shipping partner
     *
     * @param Order $order
     * @param array $fromAddress
     * @param array $toAddress
     * @return array
     */
    public function createShippingOrder(Order $order, array $fromAddress, array $toAddress): array;
    
    /**
     * Track a shipment
     *
     * @param string $trackingId
     * @return array
     */
    public function trackShipment(string $trackingId): array;
}
