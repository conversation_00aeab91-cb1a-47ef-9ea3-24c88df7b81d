<?php

namespace App\Services;

use App\Models\User;
use App\Services\WhatsAppOtpService;
use App\Utility\SendSMSUtility;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;

class UserVerificationService
{
    protected $whatsappService;
    
    public function __construct()
    {
        $this->whatsappService = new WhatsAppOtpService();
    }

    /**
     * Send phone verification OTP
     */
    public function sendPhoneVerification(User $user)
    {
        // Check rate limiting
        if (!$user->canRequestPhoneVerification()) {
            return [
                'success' => false,
                'message' => 'Please wait before requesting another verification code',
                'wait_time' => 2 - $user->last_phone_verification_attempt->diffInMinutes(now())
            ];
        }

        // Check verification attempts
        if ($user->phone_verification_attempts >= 5) {
            return [
                'success' => false,
                'message' => 'Too many verification attempts. Please try again later.',
                'blocked' => true
            ];
        }

        // Generate OTP
        $otp = rand(100000, 999999);
        
        // Update user verification data
        $user->update([
            'phone_verification_code' => $otp,
            'phone_verification_sent_at' => now(),
            'phone_verification_attempts' => $user->phone_verification_attempts + 1,
            'last_phone_verification_attempt' => now()
        ]);

        // Send via WhatsApp and SMS
        $result = $this->sendPhoneOtp($user->full_phone, $otp, $user->country_code);
        
        Log::info('Phone verification OTP sent', [
            'user_id' => $user->id,
            'phone' => $user->full_phone,
            'channels' => $result['channels_used'] ?? ['SMS'],
            'attempt' => $user->phone_verification_attempts
        ]);

        return [
            'success' => true,
            'message' => $result['message'] ?? 'Verification code sent to your phone',
            'channels' => $result['channels_used'] ?? ['SMS'],
            'attempts_left' => 5 - $user->phone_verification_attempts
        ];
    }

    /**
     * Send email verification OTP
     */
    public function sendEmailVerification(User $user)
    {
        // Check rate limiting
        if (!$user->canRequestEmailVerification()) {
            return [
                'success' => false,
                'message' => 'Please wait before requesting another verification code',
                'wait_time' => 2 - $user->last_email_verification_attempt->diffInMinutes(now())
            ];
        }

        // Check verification attempts
        if ($user->email_verification_attempts >= 5) {
            return [
                'success' => false,
                'message' => 'Too many verification attempts. Please try again later.',
                'blocked' => true
            ];
        }

        // Generate OTP
        $otp = rand(100000, 999999);
        
        // Update user verification data
        $user->update([
            'email_verification_code' => $otp,
            'email_verification_sent_at' => now(),
            'email_verification_attempts' => $user->email_verification_attempts + 1,
            'last_email_verification_attempt' => now()
        ]);

        // Send email
        try {
            $this->sendEmailOtp($user->email, $otp, $user->name);
            
            Log::info('Email verification OTP sent', [
                'user_id' => $user->id,
                'email' => $user->email,
                'attempt' => $user->email_verification_attempts
            ]);

            return [
                'success' => true,
                'message' => 'Verification code sent to your email',
                'attempts_left' => 5 - $user->email_verification_attempts
            ];
        } catch (\Exception $e) {
            Log::error('Email verification failed', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send verification email. Please try again.'
            ];
        }
    }

    /**
     * Verify phone OTP
     */
    public function verifyPhoneOtp(User $user, $otp)
    {
        // Check if OTP is valid and not expired (10 minutes)
        if (!$user->phone_verification_code || 
            $user->phone_verification_code != $otp ||
            !$user->phone_verification_sent_at ||
            $user->phone_verification_sent_at->diffInMinutes(now()) > 10) {
            
            return [
                'success' => false,
                'message' => 'Invalid or expired verification code'
            ];
        }

        // Mark phone as verified
        $user->update([
            'phone_verified_at' => now(),
            'is_phone_verified' => true,
            'phone_verification_code' => null,
            'phone_verification_sent_at' => null,
            'phone_verification_attempts' => 0
        ]);

        Log::info('Phone verified successfully', [
            'user_id' => $user->id,
            'phone' => $user->full_phone
        ]);

        return [
            'success' => true,
            'message' => 'Phone number verified successfully'
        ];
    }

    /**
     * Verify email OTP
     */
    public function verifyEmailOtp(User $user, $otp)
    {
        // Check if OTP is valid and not expired (10 minutes)
        if (!$user->email_verification_code || 
            $user->email_verification_code != $otp ||
            !$user->email_verification_sent_at ||
            $user->email_verification_sent_at->diffInMinutes(now()) > 10) {
            
            return [
                'success' => false,
                'message' => 'Invalid or expired verification code'
            ];
        }

        // Mark email as verified
        $user->update([
            'email_verified_at' => now(),
            'is_email_verified' => true,
            'email_verification_code' => null,
            'email_verification_sent_at' => null,
            'email_verification_attempts' => 0
        ]);

        Log::info('Email verified successfully', [
            'user_id' => $user->id,
            'email' => $user->email
        ]);

        return [
            'success' => true,
            'message' => 'Email address verified successfully'
        ];
    }

    /**
     * Send phone OTP via WhatsApp and SMS
     */
    private function sendPhoneOtp($phoneNumber, $otp, $countryCode = null)
    {
        return SendSMSUtility::sendOtpViaBoth($phoneNumber, $otp, $countryCode);
    }

    /**
     * Send email OTP
     */
    private function sendEmailOtp($email, $otp, $name)
    {
        $subject = 'Email Verification Code - ' . config('app.name');
        $message = "Hello {$name},\n\nYour email verification code is: {$otp}\n\nThis code will expire in 10 minutes.\n\nIf you didn't request this verification, please ignore this email.\n\nThank you,\n" . config('app.name');
        
        // For now, using basic mail. In production, use proper mail templates
        Mail::raw($message, function ($mail) use ($email, $subject) {
            $mail->to($email)->subject($subject);
        });
    }

    /**
     * Check if user needs verification
     */
    public function needsVerification(User $user)
    {
        $needs = [];
        
        if ($user->email && !$user->hasVerifiedEmail()) {
            $needs[] = 'email';
        }
        
        if ($user->phone && !$user->hasVerifiedPhone()) {
            $needs[] = 'phone';
        }
        
        return $needs;
    }

    /**
     * Get verification status
     */
    public function getVerificationStatus(User $user)
    {
        return [
            'email_verified' => $user->hasVerifiedEmail(),
            'phone_verified' => $user->hasVerifiedPhone(),
            'fully_verified' => $user->isFullyVerified(),
            'needs_verification' => $this->needsVerification($user),
            'can_request_phone_otp' => $user->canRequestPhoneVerification(),
            'can_request_email_otp' => $user->canRequestEmailVerification(),
            'phone_attempts_left' => max(0, 5 - $user->phone_verification_attempts),
            'email_attempts_left' => max(0, 5 - $user->email_verification_attempts)
        ];
    }
}
