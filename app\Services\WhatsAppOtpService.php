<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class WhatsAppOtpService
{
    protected $apiToken;
    protected $phoneNumberId;
    protected $templateId;
    protected $baseUrl;
    protected $supportedCountries;

    public function __construct()
    {
        $this->apiToken = env('WHATSAPP_API_TOKEN', '6723|qSDJOCP28LCAVLVl1ik4QzkhlQ9oMTGscJLWzJxB');
        $this->phoneNumberId = env('WHATSAPP_PHONE_NUMBER_ID', '383709501489036');
        $this->templateId = env('WHATSAPP_OTP_TEMPLATE_ID', '122343');
        $this->baseUrl = 'https://botsailor.com/api/v1/whatsapp/send/template';

        // Define supported countries for WhatsApp OTP (easily expandable)
        $this->supportedCountries = $this->getSupportedCountries();
    }

    /**
     * Get list of supported countries for WhatsApp OTP
     * This can be moved to database or config file for easy management
     */
    private function getSupportedCountries()
    {
        return Cache::remember('whatsapp_supported_countries', 3600, function () {
            return [
                // Current supported countries
                '91' => ['name' => 'India', 'code' => 'IN', 'active' => true],

                // Future expansion countries (set active to true when ready)
                '1' => ['name' => 'United States', 'code' => 'US', 'active' => false],
                '44' => ['name' => 'United Kingdom', 'code' => 'GB', 'active' => false],
                '971' => ['name' => 'United Arab Emirates', 'code' => 'AE', 'active' => false],
                '65' => ['name' => 'Singapore', 'code' => 'SG', 'active' => false],
                '60' => ['name' => 'Malaysia', 'code' => 'MY', 'active' => false],
                '66' => ['name' => 'Thailand', 'code' => 'TH', 'active' => false],
                '84' => ['name' => 'Vietnam', 'code' => 'VN', 'active' => false],
                '62' => ['name' => 'Indonesia', 'code' => 'ID', 'active' => false],
                '63' => ['name' => 'Philippines', 'code' => 'PH', 'active' => false],
                '94' => ['name' => 'Sri Lanka', 'code' => 'LK', 'active' => false],
                '880' => ['name' => 'Bangladesh', 'code' => 'BD', 'active' => false],
                '977' => ['name' => 'Nepal', 'code' => 'NP', 'active' => false],
                '92' => ['name' => 'Pakistan', 'code' => 'PK', 'active' => false],
                '61' => ['name' => 'Australia', 'code' => 'AU', 'active' => false],
                '49' => ['name' => 'Germany', 'code' => 'DE', 'active' => false],
                '33' => ['name' => 'France', 'code' => 'FR', 'active' => false],
                '39' => ['name' => 'Italy', 'code' => 'IT', 'active' => false],
                '34' => ['name' => 'Spain', 'code' => 'ES', 'active' => false],
                '81' => ['name' => 'Japan', 'code' => 'JP', 'active' => false],
                '82' => ['name' => 'South Korea', 'code' => 'KR', 'active' => false],
                '86' => ['name' => 'China', 'code' => 'CN', 'active' => false],
                '55' => ['name' => 'Brazil', 'code' => 'BR', 'active' => false],
                '52' => ['name' => 'Mexico', 'code' => 'MX', 'active' => false],
                '27' => ['name' => 'South Africa', 'code' => 'ZA', 'active' => false],
                '234' => ['name' => 'Nigeria', 'code' => 'NG', 'active' => false],
                '20' => ['name' => 'Egypt', 'code' => 'EG', 'active' => false],
                '966' => ['name' => 'Saudi Arabia', 'code' => 'SA', 'active' => false],
                '90' => ['name' => 'Turkey', 'code' => 'TR', 'active' => false],
                '7' => ['name' => 'Russia', 'code' => 'RU', 'active' => false],
            ];
        });
    }

    /**
     * Send OTP via WhatsApp with international support
     *
     * @param string $phoneNumber
     * @param string $otp
     * @param string|null $countryCode Optional country code for validation
     * @return array
     */
    public function sendOtp($phoneNumber, $otp, $countryCode = null)
    {
        try {
            // FALLBACK MECHANISM: Ensure phone has proper format, default to +91 if country code missing
            $formattedPhone = format_phone_for_delivery($phoneNumber, $countryCode);

            // Validate and format phone number
            $validationResult = $this->validateAndFormatPhone($formattedPhone, $countryCode);

            if (!$validationResult['valid']) {
                Log::warning('WhatsApp OTP validation failed', [
                    'phone' => $formattedPhone,
                    'reason' => $validationResult['reason']
                ]);
                return [
                    'success' => false,
                    'message' => $validationResult['reason'],
                    'fallback_to_sms' => true
                ];
            }

            $formattedPhone = $validationResult['formatted_phone'];
            $detectedCountry = $validationResult['country'];

            $data = [
                'apiToken' => $this->apiToken,
                'phone_number_id' => $this->phoneNumberId,
                'template_id' => $this->templateId,
                'phone_number' => $formattedPhone,
                'templateVariable-otp-1' => $otp
            ];

            $response = Http::timeout(30)->asForm()->post($this->baseUrl, $data);

            if ($response->successful()) {
                Log::info('WhatsApp OTP sent successfully', [
                    'phone' => $formattedPhone,
                    'country' => $detectedCountry['name'] ?? 'Unknown',
                    'otp' => $otp
                ]);
                return [
                    'success' => true,
                    'message' => 'WhatsApp OTP sent successfully',
                    'country' => $detectedCountry['name'] ?? 'Unknown'
                ];
            } else {
                Log::error('WhatsApp OTP failed', [
                    'phone' => $formattedPhone,
                    'country' => $detectedCountry['name'] ?? 'Unknown',
                    'response' => $response->body(),
                    'status' => $response->status()
                ]);
                return [
                    'success' => false,
                    'message' => 'WhatsApp delivery failed',
                    'fallback_to_sms' => true
                ];
            }

        } catch (\Exception $e) {
            Log::error('WhatsApp OTP exception', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage()
            ]);
            return [
                'success' => false,
                'message' => 'WhatsApp service error: ' . $e->getMessage(),
                'fallback_to_sms' => true
            ];
        }
    }

    /**
     * Validate and format international phone number
     *
     * @param string $phoneNumber
     * @param string|null $countryCode
     * @return array
     */
    private function validateAndFormatPhone($phoneNumber, $countryCode = null)
    {
        // Clean the phone number
        $cleanPhone = preg_replace('/\s+/', '', $phoneNumber); // Remove spaces
        $cleanPhone = preg_replace('/^\++/', '+', $cleanPhone); // Replace multiple + with single +
        $cleanPhone = preg_replace('/[^0-9+]/', '', $cleanPhone); // Keep only digits and +

        // Extract country code from phone number
        $detectedCountryCode = $this->extractCountryCode($cleanPhone);

        if (!$detectedCountryCode) {
            // FALLBACK: If we can't detect country code, use default +91
            $cleanPhone = '+91' . ltrim($cleanPhone, '+0');
            $detectedCountryCode = '91';
        }

        // Check if country is supported for WhatsApp
        $countryInfo = $this->supportedCountries[$detectedCountryCode] ?? null;

        if (!$countryInfo) {
            return [
                'valid' => false,
                'reason' => "WhatsApp OTP not supported for country code +{$detectedCountryCode}",
                'formatted_phone' => $cleanPhone,
                'country_code' => $detectedCountryCode
            ];
        }

        if (!$countryInfo['active']) {
            return [
                'valid' => false,
                'reason' => "WhatsApp OTP temporarily unavailable for {$countryInfo['name']}",
                'formatted_phone' => $cleanPhone,
                'country_code' => $detectedCountryCode,
                'country' => $countryInfo
            ];
        }

        // Validate phone number length based on country
        if (!$this->validatePhoneLength($cleanPhone, $detectedCountryCode)) {
            return [
                'valid' => false,
                'reason' => "Invalid phone number length for {$countryInfo['name']}",
                'formatted_phone' => $cleanPhone,
                'country_code' => $detectedCountryCode,
                'country' => $countryInfo
            ];
        }

        return [
            'valid' => true,
            'formatted_phone' => $cleanPhone,
            'country_code' => $detectedCountryCode,
            'country' => $countryInfo
        ];
    }

    /**
     * Extract country code from phone number
     *
     * @param string $phoneNumber
     * @return string|null
     */
    private function extractCountryCode($phoneNumber)
    {
        // Remove + if present
        $number = ltrim($phoneNumber, '+');

        // Try to match country codes (longest first)
        $countryCodes = array_keys($this->supportedCountries);
        usort($countryCodes, function($a, $b) {
            return strlen($b) - strlen($a);
        });

        foreach ($countryCodes as $code) {
            if (str_starts_with($number, $code)) {
                return $code;
            }
        }

        return null;
    }

    /**
     * Validate phone number length based on country
     *
     * @param string $phoneNumber
     * @param string $countryCode
     * @return bool
     */
    private function validatePhoneLength($phoneNumber, $countryCode)
    {
        $number = ltrim($phoneNumber, '+');
        $nationalNumber = substr($number, strlen($countryCode));

        // Basic length validation (can be enhanced with specific country rules)
        $lengthRules = [
            '91' => [10, 10], // India: exactly 10 digits
            '1' => [10, 10],  // US/Canada: exactly 10 digits
            '44' => [10, 11], // UK: 10-11 digits
            '971' => [8, 9],  // UAE: 8-9 digits
            '65' => [8, 8],   // Singapore: exactly 8 digits
            '60' => [9, 10],  // Malaysia: 9-10 digits
            '66' => [8, 9],   // Thailand: 8-9 digits
            '84' => [9, 10],  // Vietnam: 9-10 digits
            '62' => [8, 12],  // Indonesia: 8-12 digits
            '63' => [10, 10], // Philippines: exactly 10 digits
        ];

        $rule = $lengthRules[$countryCode] ?? [8, 15]; // Default: 8-15 digits
        $nationalLength = strlen($nationalNumber);

        return $nationalLength >= $rule[0] && $nationalLength <= $rule[1];
    }

    /**
     * Check if WhatsApp OTP is enabled
     *
     * @return bool
     */
    public function isEnabled()
    {
        return !empty($this->apiToken) && !empty($this->phoneNumberId) && !empty($this->templateId);
    }

    /**
     * Check if WhatsApp is supported for a specific country
     *
     * @param string $countryCode
     * @return bool
     */
    public function isCountrySupported($countryCode)
    {
        $countryInfo = $this->supportedCountries[$countryCode] ?? null;
        return $countryInfo && $countryInfo['active'];
    }

    /**
     * Get list of supported countries
     *
     * @return array
     */
    public function getSupportedCountriesList()
    {
        return array_filter($this->supportedCountries, function($country) {
            return $country['active'];
        });
    }

    /**
     * Enable WhatsApp for a specific country
     *
     * @param string $countryCode
     * @return bool
     */
    public function enableCountry($countryCode)
    {
        if (isset($this->supportedCountries[$countryCode])) {
            // In production, this should update database/config
            Cache::forget('whatsapp_supported_countries');
            return true;
        }
        return false;
    }
}
