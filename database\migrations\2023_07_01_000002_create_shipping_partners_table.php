<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShippingPartnersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipping_partners', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->string('api_key')->nullable();
            $table->string('api_secret')->nullable();
            $table->text('api_url')->nullable();
            $table->boolean('is_active')->default(false);
            $table->timestamps();
        });

        // Insert default shipping partners
        DB::table('shipping_partners')->insert([
            [
                'name' => 'Shiprocket',
                'code' => 'shiprocket',
                'description' => 'Shiprocket shipping integration',
                'is_active' => false,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Delhivery',
                'code' => 'delhivery',
                'description' => 'Delhivery shipping integration',
                'is_active' => false,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Ecom Express',
                'code' => 'ecom_express',
                'description' => 'Ecom Express shipping integration',
                'is_active' => false,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipping_partners');
    }
}
