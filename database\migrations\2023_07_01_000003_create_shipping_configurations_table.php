<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShippingConfigurationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipping_configurations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('shipping_partner_id');
            $table->boolean('is_active')->default(false);
            $table->boolean('use_shipping_rate_calculator')->default(false);
            $table->text('cloudmart_fulfillment_address')->nullable();
            $table->timestamps();
            
            $table->foreign('shipping_partner_id')->references('id')->on('shipping_partners')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipping_configurations');
    }
}
