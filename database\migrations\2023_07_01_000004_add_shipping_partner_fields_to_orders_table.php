<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddShippingPartnerFieldsToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->unsignedBigInteger('shipping_partner_id')->nullable()->after('carrier_id');
            $table->string('tracking_id')->nullable()->after('shipping_partner_id');
            $table->text('shipping_partner_response')->nullable()->after('tracking_id');
            
            $table->foreign('shipping_partner_id')->references('id')->on('shipping_partners')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropForeign(['shipping_partner_id']);
            $table->dropColumn(['shipping_partner_id', 'tracking_id', 'shipping_partner_response']);
        });
    }
}
