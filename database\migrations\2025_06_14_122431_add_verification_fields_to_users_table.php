<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            // Check and add columns only if they don't exist
            if (!Schema::hasColumn('users', 'country_code')) {
                $table->string('country_code', 10)->nullable()->after('phone');
            }

            if (!Schema::hasColumn('users', 'phone_verified_at')) {
                $table->timestamp('phone_verified_at')->nullable()->after('email_verified_at');
            }

            if (!Schema::hasColumn('users', 'phone_verification_code')) {
                $table->string('phone_verification_code', 10)->nullable()->after('verification_code');
            }

            if (!Schema::hasColumn('users', 'phone_verification_sent_at')) {
                $table->timestamp('phone_verification_sent_at')->nullable()->after('phone_verification_code');
            }

            if (!Schema::hasColumn('users', 'email_verification_code')) {
                $table->string('email_verification_code', 10)->nullable()->after('phone_verification_sent_at');
            }

            if (!Schema::hasColumn('users', 'email_verification_sent_at')) {
                $table->timestamp('email_verification_sent_at')->nullable()->after('email_verification_code');
            }

            if (!Schema::hasColumn('users', 'is_phone_verified')) {
                $table->boolean('is_phone_verified')->default(false)->after('email_verification_sent_at');
            }

            if (!Schema::hasColumn('users', 'is_email_verified')) {
                $table->boolean('is_email_verified')->default(false)->after('is_phone_verified');
            }

            if (!Schema::hasColumn('users', 'phone_verification_attempts')) {
                $table->integer('phone_verification_attempts')->default(0)->after('is_email_verified');
            }

            if (!Schema::hasColumn('users', 'email_verification_attempts')) {
                $table->integer('email_verification_attempts')->default(0)->after('phone_verification_attempts');
            }

            if (!Schema::hasColumn('users', 'last_phone_verification_attempt')) {
                $table->timestamp('last_phone_verification_attempt')->nullable()->after('email_verification_attempts');
            }

            if (!Schema::hasColumn('users', 'last_email_verification_attempt')) {
                $table->timestamp('last_email_verification_attempt')->nullable()->after('last_phone_verification_attempt');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'country_code',
                'phone_verified_at',
                'phone_verification_code',
                'phone_verification_sent_at',
                'email_verification_code',
                'email_verification_sent_at',
                'is_phone_verified',
                'is_email_verified',
                'phone_verification_attempts',
                'email_verification_attempts',
                'last_phone_verification_attempt',
                'last_email_verification_attempt'
            ]);
        });
    }
};
