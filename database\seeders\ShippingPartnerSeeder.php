<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ShippingPartner;
use App\Models\ShippingConfiguration;

class ShippingPartnerSeeder extends Seeder
{
    /**
     * Run the database seeder.
     *
     * @return void
     */
    public function run()
    {
        // Create Shiprocket
        $shiprocket = ShippingPartner::updateOrCreate(
            ['code' => 'shiprocket'],
            [
                'name' => 'Shiprocket',
                'description' => 'Shiprocket shipping integration for fast and reliable delivery',
                'api_url' => 'https://apiv2.shiprocket.in/v1/external',
                'is_active' => false
            ]
        );

        ShippingConfiguration::updateOrCreate(
            ['shipping_partner_id' => $shiprocket->id],
            [
                'is_active' => false,
                'use_shipping_rate_calculator' => true,
                'cloudmart_fulfillment_address' => json_encode([
                    'name' => 'Cloud Mart Fulfillment Center',
                    'address' => 'Cloud Mart Warehouse, Industrial Area',
                    'city' => 'Mumbai',
                    'state' => 'Maharashtra',
                    'country' => 'India',
                    'postal_code' => '400001',
                    'phone' => '9999999999'
                ])
            ]
        );

        // Create Delhivery
        $delhivery = ShippingPartner::updateOrCreate(
            ['code' => 'delhivery'],
            [
                'name' => 'Delhivery',
                'description' => 'Delhivery shipping integration for pan-India delivery',
                'api_url' => 'https://track.delhivery.com/api',
                'is_active' => false
            ]
        );

        ShippingConfiguration::updateOrCreate(
            ['shipping_partner_id' => $delhivery->id],
            [
                'is_active' => false,
                'use_shipping_rate_calculator' => true,
                'cloudmart_fulfillment_address' => json_encode([
                    'name' => 'Cloud Mart Fulfillment Center',
                    'address' => 'Cloud Mart Warehouse, Industrial Area',
                    'city' => 'Mumbai',
                    'state' => 'Maharashtra',
                    'country' => 'India',
                    'postal_code' => '400001',
                    'phone' => '9999999999'
                ])
            ]
        );

        // Create Ecom Express
        $ecomExpress = ShippingPartner::updateOrCreate(
            ['code' => 'ecom_express'],
            [
                'name' => 'Ecom Express',
                'description' => 'Ecom Express shipping integration for e-commerce delivery',
                'api_url' => 'https://api.ecomexpress.in',
                'is_active' => false
            ]
        );

        ShippingConfiguration::updateOrCreate(
            ['shipping_partner_id' => $ecomExpress->id],
            [
                'is_active' => false,
                'use_shipping_rate_calculator' => true,
                'cloudmart_fulfillment_address' => json_encode([
                    'name' => 'Cloud Mart Fulfillment Center',
                    'address' => 'Cloud Mart Warehouse, Industrial Area',
                    'city' => 'Mumbai',
                    'state' => 'Maharashtra',
                    'country' => 'India',
                    'postal_code' => '400001',
                    'phone' => '9999999999'
                ])
            ]
        );

        $this->command->info('Shipping partners seeded successfully!');
        $this->command->info('Please configure API credentials in the admin panel to activate the partners.');
    }
}
