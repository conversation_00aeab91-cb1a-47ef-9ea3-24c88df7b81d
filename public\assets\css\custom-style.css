/*your custom css goes here*/

/* OTP Resend <PERSON><PERSON> Styles */
.btn-link.btn-sm {
    font-size: 12px;
    text-decoration: none;
    color: #007bff;
    border: none;
    background: none;
    padding: 0;
    cursor: pointer;
}

.btn-link.btn-sm:hover {
    color: #0056b3;
    text-decoration: underline;
}

.btn-link.btn-sm:disabled {
    color: #6c757d;
    cursor: not-allowed;
    text-decoration: none;
}

.btn-link.btn-sm:disabled:hover {
    color: #6c757d;
    text-decoration: none;
}

/* Dashboard Summary Cards */
.dashboard-summary-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef !important;
}

.dashboard-summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
    border-color: #dee2e6 !important;
}

.dashboard-summary-card .icon-circle {
    transition: all 0.3s ease;
}

.dashboard-summary-card:hover .icon-circle {
    transform: scale(1.1);
}

.dashboard-summary-card .fs-28 {
    font-size: 2rem !important;
    line-height: 1.2;
}

.dashboard-summary-card .text-primary {
    transition: color 0.3s ease;
}

.dashboard-summary-card .text-primary:hover {
    color: #0056b3 !important;
    text-decoration: none;
}

.rounded-lg {
    border-radius: 12px !important;
}

.shadow-sm {
    box-shadow: 0 2px 8px rgba(0,0,0,0.08) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dashboard-summary-card .fs-28 {
        font-size: 1.75rem !important;
    }

    .dashboard-summary-card .icon-circle {
        width: 50px !important;
        height: 50px !important;
    }

    .dashboard-summary-card .icon-circle svg {
        width: 24px !important;
        height: 24px !important;
    }
}

/* Product Details Page Enhancements */
.product__details--info__title {
    line-height: 1.4;
    margin-bottom: 1rem;
}

.product__details--info__desc {
    line-height: 1.6;
    color: #6c757d;
}

.product__details--info__price .current__price {
    font-weight: 700;
    color: #007bff;
}

.product__details--info__price .old__price {
    text-decoration: line-through;
    color: #6c757d;
}

.rating {
    gap: 2px;
}

.variant__size--value {
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
}

.variant__size--value:hover,
input[type="radio"]:checked + .variant__size--value {
    border-color: #007bff;
    background-color: #007bff;
    color: white;
}

.color-option {
    transition: all 0.3s ease;
    padding: 8px 12px;
    border-radius: 6px;
}

.color-option:hover {
    background-color: #f8f9fa;
}

input[type="radio"]:checked + .color-option {
    background-color: #e3f2fd;
    border: 1px solid #007bff;
}

.color-swatch {
    border: 2px solid #dee2e6 !important;
    transition: all 0.3s ease;
}

input[type="radio"]:checked + .color-option .color-swatch {
    border-color: #007bff !important;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.product__variant--title {
    color: #495057;
    font-weight: 600;
}

.bg-light {
    background-color: #f8f9fa !important;
}

.text-primary {
    color: #007bff !important;
}

.text-muted {
    color: #6c757d !important;
}

.badge-danger {
    background-color: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
}

.btn-outline-primary {
    border-color: #007bff;
    color: #007bff;
}

.btn-outline-primary:hover {
    background-color: #007bff;
    color: white;
}

.cursor-pointer {
    cursor: pointer;
}
