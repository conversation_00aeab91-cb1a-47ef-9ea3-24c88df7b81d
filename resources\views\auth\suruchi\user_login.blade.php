{{--@extends('auth.layouts.authentication')--}}
@extends('frontend.suruchi.layouts.app')

@section('meta')
    @if (get_setting('otp_system'))
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/css/intlTelInput.css">
        <style>
            .phone-form-group .iti {
                width: 100%;
            }
            .phone-form-group .iti__country-list {
                z-index: 9999;
            }
            .phone-form-group .iti__selected-flag {
                padding: 0 8px;
            }
            .phone-form-group .account__login--input {
                padding-left: 60px;
            }
            .phone-form-group .iti__flag-container {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                padding: 1px;
            }
            .phone-form-group {
                position: relative;
            }
        </style>
    @endif
@endsection

@section('content')

    <div class="login__section section--padding">
        <div class="container">
            <form class="form-default account__login--inner" role="form" action="{{ route('login') }}" method="POST">
                @csrf
                <div class="login__section--inner">
                    <div class="row">
                        <div class="col-md-3 col-lg-3 col-sm-0"></div>
                        <div class="col-md-6 col-lg-6">
                            <div class="account__login">
                                <div class="account__login--header mb-25">
                                    <h2 class="account__login--header__title h3 mb-10">{{ translate('Welcome Back!')}}</h2>
                                    <p class="account__login--header__desc">Login if you are a returning customer.</p>
                                </div>
                                <div class="account__login--inner">
                                    <!-- Conditional Email/Phone Login -->
                                    @if (get_setting('otp_system'))
                                        <div class="form-group phone-form-group mb-1">
                                            <input type="tel" id="phone-code" class="account__login--input" name="phone" placeholder="{{  translate('Phone') }}" value="{{ old('phone') }}" autocomplete="off">
                                        </div>
                                        <input type="hidden" name="country_code" value="+91">
                                        <div class="form-group email-form-group mb-1 d-none">
                                            <input type="email" class="account__login--input" name="email" placeholder="{{ translate('Email') }}" value="{{ old('email') }}" autocomplete="off">
                                            @error('email')
                                            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                                            @enderror
                                        </div>
                                        <button type="button" class="btn btn-link p-0 text-primary fs-12" onclick="toggleEmailPhone(this)">
                                            <i>*{{ translate('Use Email Instead') }}</i>
                                        </button>
                                    @else
                                        <div class="form-group">
                                            <input type="email" class="account__login--input" placeholder="{{  translate('Email') }}" name="email" value="{{ old('email') }}" autocomplete="off">
                                            @error('email')
                                            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                                            @enderror
                                        </div>
                                    @endif

                                    <!-- Phone OTP Field (for phone login) -->
                                    @if (get_setting('otp_system'))
                                        <div id="phone-otp-input-group" class="form-group d-none">
                                            <input type="text" id="phone_otp" name="phone_otp" class="account__login--input" placeholder="{{ translate('Enter Phone OTP') }}" autocomplete="off">
                                            <div class="text-center mt-2">
                                                <small class="text-muted">{{ translate("Didn't receive OTP?") }}</small>
                                                <button type="button" id="resend-phone-otp" class="btn btn-link btn-sm p-0 ml-1" onclick="resendPhoneOtp()">
                                                    {{ translate('Resend OTP') }}
                                                </button>
                                                <span id="phone-otp-timer" class="text-muted ml-1" style="display: none;"></span>
                                            </div>
                                        </div>
                                    @endif

                                    <!-- Email OTP Field (for email OTP login) -->
                                    @if (get_setting('ask_email_otp_to_login'))
                                        <div id="email-otp-input-group" class="form-group d-none">
                                            <input type="text" id="email_otp" name="email_otp" class="account__login--input" placeholder="{{ translate('Enter Email OTP') }}" autocomplete="off">
                                            <div class="text-center mt-2">
                                                <small class="text-muted">{{ translate("Didn't receive OTP?") }}</small>
                                                <button type="button" id="resend-email-otp" class="btn btn-link btn-sm p-0 ml-1" onclick="resendEmailOtp()">
                                                    {{ translate('Resend OTP') }}
                                                </button>
                                                <span id="email-otp-timer" class="text-muted ml-1" style="display: none;"></span>
                                            </div>
                                        </div>
                                    @endif

                                    <!-- Password Field (for regular login) -->
                                    @if (!get_setting('ask_email_otp_to_login'))
                                        <div id="password-input-group" class="form-group">
                                            <label for="password" class="fs-12 fw-700 text-soft-dark"></label>
                                            <div class="position-relative">
                                                <input type="password" id="password" class="account__login--input" name="password" placeholder="{{ translate('Password')}}">
                                                <i class="password-toggle las la-eye"></i>
                                            </div>
                                        </div>
                                    @endif

                                    <div class="account__login--remember__forgot mb-15 d-flex justify-content-between align-items-center">
                                        <div class="account__login--remember position__relative">
                                            <input class="checkout__checkbox--input" id="check1" type="checkbox" {{ old('remember') ? 'checked' : '' }}>
                                            <span class="checkout__checkbox--checkmark"></span>
                                            <label class="checkout__checkbox--label login__remember--label" for="check1">
                                                Remember me</label>
                                        </div>
                                        <a href="{{ route('password.request') }}" class="account__login--forgot">Forgot Your Password?</a>
                                    </div>

                                    <!-- Dynamic Login Button -->
                                    <button id="login-btn" class="account__login--btn primary__btn" type="button" onclick="handleLogin()">
                                        {{ translate('Login') }}
                                    </button>

                                    <!-- Social Login -->
                                    @if(get_setting('google_login') || get_setting('facebook_login') || get_setting('twitter_login') || get_setting('apple_login'))
                                        <div class="account__login--divide"><span class="account__login--divide__text">OR</span></div>
                                        <div class="account__social d-flex justify-content-center mb-15">
                                            @if(get_setting('facebook_login'))
                                                <a href="{{ route('social.login', ['provider' => 'facebook']) }}" class="account__social--link facebook">Facebook</a>
                                            @endif
                                            @if(get_setting('google_login'))
                                                <a href="{{ route('social.login', ['provider' => 'google']) }}" class="account__social--link google">Google</a>
                                            @endif
                                            @if(get_setting('twitter_login'))
                                                <a href="{{ route('social.login', ['provider' => 'twitter']) }}" class="account__social--link twitter">Twitter</a>
                                            @endif
                                            @if(get_setting('apple_login'))
                                                <a href="{{ route('social.login', ['provider' => 'apple']) }}" class="account__social--link apple">Apple</a>
                                            @endif
                                        </div>
                                    @endif

                                    <!-- Register Now and Back Links -->
                                    <p class="account__login--signup__text">{{ translate("Don't have an account?") }}
                                        <a href="{{ route('user.registration') }}">Register now</a>
                                    </p>
                                    <a href="{{ url()->previous() }}" class="mt-3 fs-14 fw-700 text-primary">
                                        <i class="las la-arrow-left fs-20 mr-1"></i>{{ translate('Back to Previous Page') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('script')
    @if (get_setting('otp_system'))
        <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/intlTelInput.min.js"></script>
    @endif
    <script type="text/javascript">
        function autoFillCustomer(){
            $('#email').val('<EMAIL>');
            $('#password').val('123456');
        }

        // Toggle between email and phone input
        function toggleEmailPhone(button) {
            const phoneGroup = $('.phone-form-group');
            const emailGroup = $('.email-form-group');
            const phoneOtpGroup = $('#phone-otp-input-group');

            if (phoneGroup.hasClass('d-none')) {
                // Switch to phone
                phoneGroup.removeClass('d-none');
                emailGroup.addClass('d-none');
                phoneOtpGroup.addClass('d-none');
                $(button).html('<i>*{{ translate("Use Email Instead") }}</i>');
                $('#login-btn').text('{{ translate("Send OTP") }}');
            } else {
                // Switch to email
                phoneGroup.addClass('d-none');
                emailGroup.removeClass('d-none');
                phoneOtpGroup.addClass('d-none');
                $(button).html('<i>*{{ translate("Use Phone Instead") }}</i>');
                updateLoginButtonText();
            }
        }

        // Update login button text based on current mode
        function updateLoginButtonText() {
            @if (get_setting('ask_email_otp_to_login'))
                $('#login-btn').text('{{ translate("Send OTP") }}');
            @else
                $('#login-btn').text('{{ translate("Login") }}');
            @endif
        }

        // Unified login handler
        function handleLogin() {
            const isPhoneVisible = !$('.phone-form-group').hasClass('d-none');
            const isEmailVisible = !$('.email-form-group').hasClass('d-none');

            if (isPhoneVisible) {
                handlePhoneLogin();
            } else if (isEmailVisible) {
                @if (get_setting('ask_email_otp_to_login'))
                    handleEmailOtpLogin();
                @else
                    handleRegularLogin();
                @endif
            } else {
                handleRegularLogin();
            }
        }

        // Handle phone OTP login
        function handlePhoneLogin() {
            const phoneInput = $('#phone-code');
            const phoneOtp = $('#phone_otp').val();

            if (phoneOtp.trim() !== '') {
                // Submit form with phone OTP
                submitLoginForm();
            } else {
                // Get phone number and country code from intl-tel-input
                let phone, countryCode;

                if (window.iti) {
                    // Use intl-tel-input if available
                    phone = window.iti.getNumber().replace('+' + window.iti.getSelectedCountryData().dialCode, '');
                    countryCode = '+' + window.iti.getSelectedCountryData().dialCode;
                } else {
                    // Fallback to manual input
                    phone = phoneInput.val();
                    countryCode = $('input[name="country_code"]').val();
                }

                if (!phone.trim()) {
                    alert('{{ translate("Please enter phone number") }}');
                    return;
                }

                $.ajax({
                    url: "{{ route('user.login.send-otp') }}",
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}",
                        phone: phone,
                        country_code: countryCode
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            $('#phone-otp-input-group').removeClass('d-none');
                            $('#login-btn').text('{{ translate("Login") }}');
                            alert('{{ translate("OTP sent to your phone") }}');
                        } else {
                            alert(response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            alert(response.message || 'Error sending OTP');
                        } catch (e) {
                            alert('Error sending OTP: ' + xhr.responseText);
                        }
                    }
                });
            }
        }

        // Resend Phone OTP
        function resendPhoneOtp() {
            const phone = $('input[name="phone"]').val();
            const countryCode = $('#phone').intlTelInput('getSelectedCountryData').dialCode;

            if (!phone) {
                alert('{{ translate("Please enter phone number first") }}');
                return;
            }

            // Disable resend button and start timer
            $('#resend-phone-otp').prop('disabled', true);
            startResendTimer('phone');

            $.ajax({
                url: "{{ route('user.login.send-otp') }}",
                type: "POST",
                data: {
                    _token: "{{ csrf_token() }}",
                    phone: phone,
                    country_code: countryCode
                },
                success: function(response) {
                    if (response.status === 'success') {
                        alert('{{ translate("OTP resent to your phone") }}');
                    } else {
                        alert(response.message);
                        // Re-enable button if failed
                        $('#resend-phone-otp').prop('disabled', false);
                        $('#phone-otp-timer').hide();
                    }
                },
                error: function(xhr, status, error) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        alert(response.message || 'Error resending OTP');
                    } catch (e) {
                        alert('Error resending OTP: ' + xhr.responseText);
                    }
                    // Re-enable button if failed
                    $('#resend-phone-otp').prop('disabled', false);
                    $('#phone-otp-timer').hide();
                }
            });
        }

        // Resend Email OTP
        function resendEmailOtp() {
            const email = $('input[name="email"]').val();

            if (!email) {
                alert('{{ translate("Please enter email first") }}');
                return;
            }

            // Disable resend button and start timer
            $('#resend-email-otp').prop('disabled', true);
            startResendTimer('email');

            $.ajax({
                url: "{{ route('user.login.send-otp') }}",
                type: "POST",
                data: {
                    _token: "{{ csrf_token() }}",
                    email: email
                },
                success: function(response) {
                    if (response.status === 'success') {
                        alert('{{ translate("OTP resent to your email") }}');
                    } else {
                        alert(response.message);
                        // Re-enable button if failed
                        $('#resend-email-otp').prop('disabled', false);
                        $('#email-otp-timer').hide();
                    }
                },
                error: function(xhr, status, error) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        alert(response.message || 'Error resending OTP');
                    } catch (e) {
                        alert('Error resending OTP: ' + xhr.responseText);
                    }
                    // Re-enable button if failed
                    $('#resend-email-otp').prop('disabled', false);
                    $('#email-otp-timer').hide();
                }
            });
        }

        // Start resend timer
        function startResendTimer(type) {
            let seconds = 60;
            const timerElement = $(`#${type}-otp-timer`);
            const buttonElement = $(`#resend-${type}-otp`);

            timerElement.show();

            const timer = setInterval(function() {
                timerElement.text(`(${seconds}s)`);
                seconds--;

                if (seconds < 0) {
                    clearInterval(timer);
                    timerElement.hide();
                    buttonElement.prop('disabled', false);
                }
            }, 1000);
        }

        // Handle email OTP login
        function handleEmailOtpLogin() {
            const email = $('input[name="email"]').val();
            const emailOtp = $('#email_otp').val();

            if (emailOtp.trim() !== '') {
                // Submit form with email OTP
                submitLoginForm();
            } else {
                // Send email OTP
                if (!email.trim()) {
                    alert('{{ translate("Please enter email address") }}');
                    return;
                }

                $.ajax({
                    url: "{{ route('user.login.send-otp') }}",
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}",
                        email: email
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            $('#email-otp-input-group').removeClass('d-none');
                            $('#login-btn').text('{{ translate("Login") }}');
                            alert('{{ translate("OTP sent to your email") }}');
                        } else {
                            alert(response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            alert(response.message || 'Error sending OTP');
                        } catch (e) {
                            alert('Error sending OTP: ' + xhr.responseText);
                        }
                    }
                });
            }
        }

        // Handle regular email/password login
        function handleRegularLogin() {
            const email = $('input[name="email"]').val();
            const password = $('#password').val();

            if (!email.trim()) {
                alert('{{ translate("Please enter email address") }}');
                return;
            }

            if (!password.trim()) {
                alert('{{ translate("Please enter password") }}');
                return;
            }

            submitLoginForm();
        }

        // Submit the login form
        function submitLoginForm() {
            const form = $('.form-default')[0];
            form.action = "{{ route('cart.login.submit') }}";
            form.submit();
        }

        @if (get_setting('otp_system'))
        // Initialize intl-tel-input for phone login
        $(document).ready(function() {
            var phoneInput = document.querySelector("#phone-code");
            if (phoneInput) {
                window.iti = window.intlTelInput(phoneInput, {
                    initialCountry: "auto",
                    geoIpLookup: function(callback) {
                        fetch('https://ipapi.co/json')
                            .then(function(res) { return res.json(); })
                            .then(function(data) { callback(data.country_code); })
                            .catch(function() { callback("in"); }); // Default to India
                    },
                    utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/utils.js",
                    separateDialCode: true,
                    nationalMode: false,
                    formatOnDisplay: true,
                    autoPlaceholder: "aggressive"
                });

                // Update country code when country changes
                phoneInput.addEventListener('countrychange', function() {
                    var countryCode = window.iti.getSelectedCountryData().dialCode;
                    $('input[name="country_code"]').val('+' + countryCode);
                });

                // Set initial country code
                phoneInput.addEventListener('ready', function() {
                    var countryCode = window.iti.getSelectedCountryData().dialCode;
                    $('input[name="country_code"]').val('+' + countryCode);
                });

                // Update phone number format on form submission
                $('.form-default').on('submit', function() {
                    var phoneNumber = window.iti.getNumber();
                    if (phoneNumber) {
                        $('#phone-code').val(phoneNumber);
                    }
                });
            }
        });
        @endif
    </script>
@endsection
