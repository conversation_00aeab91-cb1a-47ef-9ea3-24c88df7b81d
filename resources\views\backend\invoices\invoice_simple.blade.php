<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>INVOICE</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            font-size: 12px; 
            margin: 20px; 
        }
        .header { 
            text-align: center; 
            margin-bottom: 20px; 
        }
        .info { 
            margin-bottom: 20px; 
        }
        .products { 
            width: 100%; 
            border-collapse: collapse; 
            margin-bottom: 20px; 
        }
        .products th, .products td { 
            border: 1px solid #ddd; 
            padding: 8px; 
            text-align: left; 
        }
        .products th { 
            background-color: #f2f2f2; 
        }
        .totals { 
            text-align: right; 
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>INVOICE</h1>
        <p>Order ID: {{ $order->code ?? 'N/A' }}</p>
        <p>Date: {{ date('d-m-Y', $order->date ?? time()) }}</p>
    </div>

    <div class="info">
        <h3>Bill To:</h3>
        @php
            $shipping_address = null;
            if ($order->shipping_address) {
                try {
                    $shipping_address = json_decode($order->shipping_address);
                } catch (Exception $e) {
                    $shipping_address = null;
                }
            }
        @endphp
        <p><strong>{{ $shipping_address->name ?? 'N/A' }}</strong></p>
        <p>{{ $shipping_address->address ?? 'N/A' }}</p>
        <p>{{ $shipping_address->city ?? 'N/A' }}, {{ $shipping_address->postal_code ?? 'N/A' }}</p>
        <p>{{ $shipping_address->country ?? 'N/A' }}</p>
        <p>Email: {{ $shipping_address->email ?? 'N/A' }}</p>
        <p>Phone: {{ $shipping_address->phone ?? 'N/A' }}</p>
    </div>

    <table class="products">
        <thead>
            <tr>
                <th>Product</th>
                <th>Qty</th>
                <th>Price</th>
                <th>Tax</th>
                <th>Total</th>
            </tr>
        </thead>
        <tbody>
            @if($order->orderDetails)
                @foreach ($order->orderDetails as $orderDetail)
                    @if ($orderDetail->product)
                        <tr>
                            <td>{{ $orderDetail->product->name ?? 'N/A' }}</td>
                            <td>{{ $orderDetail->quantity ?? 0 }}</td>
                            <td>{{ single_price($orderDetail->quantity > 0 ? $orderDetail->price/$orderDetail->quantity : 0) }}</td>
                            <td>{{ single_price($orderDetail->quantity > 0 ? $orderDetail->tax/$orderDetail->quantity : 0) }}</td>
                            <td>{{ single_price(($orderDetail->price ?? 0) + ($orderDetail->tax ?? 0)) }}</td>
                        </tr>
                    @endif
                @endforeach
            @endif
        </tbody>
    </table>

    <div class="totals">
        <p><strong>Sub Total: {{ single_price($order->orderDetails->sum('price') ?? 0) }}</strong></p>
        <p><strong>Shipping: {{ single_price($order->orderDetails->sum('shipping_cost') ?? 0) }}</strong></p>
        <p><strong>Tax: {{ single_price($order->orderDetails->sum('tax') ?? 0) }}</strong></p>
        <p><strong>Discount: {{ single_price($order->coupon_discount ?? 0) }}</strong></p>
        <p><strong>Grand Total: {{ single_price($order->grand_total ?? 0) }}</strong></p>
    </div>
</body>
</html>
