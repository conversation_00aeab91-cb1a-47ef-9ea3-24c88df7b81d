@extends('backend.layouts.app')

@section('content')
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0 h6">{{ translate('PhonePe Credential') }}</h5>
                </div>
                <div class="card-body">
                    <form class="form-horizontal" action="{{ route('phonepe.update_credentials') }}" method="POST">
                        @csrf
                        <div class="form-group row">
                            <input type="hidden" name="types[]" value="PHONEPE_CLIENT_ID">
                            <div class="col-md-4">
                                <label class="col-from-label">{{ translate('PhonePe Client ID') }}</label>
                            </div>
                            <div class="col-md-8">
                                <input type="text" class="form-control" name="PHONEPE_CLIENT_ID"
                                    value="{{ env('PHONEPE_CLIENT_ID') }}"
                                    placeholder="{{ translate('PhonePe Client ID') }}" required>
                            </div>
                        </div>
                        <div class="form-group row">
                            <input type="hidden" name="types[]" value="PHONEPE_CLIENT_VERSION">
                            <div class="col-md-4">
                                <label class="col-from-label">{{ translate('PhonePe Client Version') }}</label>
                            </div>
                            <div class="col-md-8">
                                <input type="text" class="form-control" name="PHONEPE_CLIENT_VERSION"
                                    value="{{ env('PHONEPE_CLIENT_VERSION') }}"
                                    placeholder="{{ translate('PhonePe Client Version') }}" required>
                                <small class="text-muted">Use 1 for UAT/Sandbox mode</small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <input type="hidden" name="types[]" value="PHONEPE_CLIENT_SECRET">
                            <div class="col-md-4">
                                <label class="col-from-label">{{ translate('PhonePe Client Secret') }}</label>
                            </div>
                            <div class="col-md-8">
                                <input type="text" class="form-control" name="PHONEPE_CLIENT_SECRET"
                                    value="{{ env('PHONEPE_CLIENT_SECRET') }}"
                                    placeholder="{{ translate('PhonePe Client Secret') }}" required>
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="col-md-4">
                                <label class="col-from-label">{{ translate('PhonePe Sandbox Mode') }}</label>
                            </div>
                            <div class="col-md-8">
                                <label class="aiz-switch aiz-switch-success mb-0">
                                    <input type="checkbox" name="phonepe_sandbox" @if (get_setting('phonepe_sandbox') == 1) checked @endif>
                                    <span class="slider round"></span>
                                </label>
                            </div>
                        </div>
                        <div class="form-group mb-0 text-right">
                            <button type="submit" class="btn btn-sm btn-primary">{{ translate('Save') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
