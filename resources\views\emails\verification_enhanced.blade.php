<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{ $array['subject'] }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
        }
        
        .email-container {
            max-width: 600px;
            margin: 40px auto;
            background: #ffffff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }
        
        .email-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .logo {
            position: relative;
            z-index: 2;
            max-width: 150px;
            height: auto;
            margin-bottom: 20px;
            filter: brightness(0) invert(1);
        }
        
        .email-icon {
            position: relative;
            z-index: 2;
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .email-icon svg {
            width: 40px;
            height: 40px;
            fill: white;
        }
        
        .header-title {
            position: relative;
            z-index: 2;
            color: white;
            font-size: 28px;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .email-body {
            padding: 50px 40px;
            text-align: center;
        }
        
        .welcome-text {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .description {
            font-size: 16px;
            color: #6c757d;
            line-height: 1.8;
            margin-bottom: 40px;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .verify-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 18px 40px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            margin-bottom: 30px;
        }
        
        .verify-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        
        .security-note {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 30px 0;
            border-radius: 8px;
            text-align: left;
        }
        
        .security-note h4 {
            color: #2c3e50;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .security-note p {
            color: #6c757d;
            font-size: 14px;
            margin: 0;
            line-height: 1.6;
        }
        
        .alternative-link {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 30px 0;
        }
        
        .alternative-link p {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .alternative-link code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
            color: #495057;
        }
        
        .email-footer {
            background: #f8f9fa;
            padding: 30px 40px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .footer-text {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .social-links {
            margin: 20px 0;
        }
        
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #6c757d;
            text-decoration: none;
            font-size: 14px;
        }
        
        .company-info {
            color: #adb5bd;
            font-size: 12px;
            margin-top: 20px;
        }
        
        @media only screen and (max-width: 600px) {
            .email-container {
                margin: 20px;
                border-radius: 15px;
            }
            
            .email-body {
                padding: 30px 20px;
            }
            
            .email-header {
                padding: 30px 20px;
            }
            
            .header-title {
                font-size: 24px;
            }
            
            .welcome-text {
                font-size: 20px;
            }
            
            .verify-button {
                padding: 15px 30px;
                font-size: 15px;
            }
            
            .email-footer {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            @php
            $logo = get_setting('header_logo');
            @endphp
            @if($logo)
                <img src="{{ uploaded_asset($logo) }}" alt="{{ get_setting('site_name') }}" class="logo">
            @endif
            
            <div class="email-icon">
                <svg viewBox="0 0 24 24">
                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                </svg>
            </div>
            
            <h1 class="header-title">{{ translate('Verify Your Email') }}</h1>
        </div>
        
        <!-- Body -->
        <div class="email-body">
            <h2 class="welcome-text">
                @if(!empty($array['user_name']))
                    {{ translate('Hello') }} {{ $array['user_name'] }}!
                @else
                    {{ translate('Welcome to') }} {{ get_setting('site_name') }}!
                @endif
            </h2>

            <p class="description">
                {{ translate('Thank you for joining our community! To complete your registration and secure your account, please verify your email address by clicking the button below.') }}
            </p>

            @if(!empty($array['link']))
                <a href="{{ $array['link'] }}" class="verify-button" target="_blank">
                    ✉️ {{ translate('Verify Email Address') }}
                </a>
            @endif

            <div class="security-note">
                <h4>🔒 {{ translate('Security Notice') }}</h4>
                <p>{{ translate('This verification link will expire in 24 hours for your security. If you did not create an account with us, please ignore this email and no account will be created.') }}</p>
            </div>

            @if(!empty($array['link']))
                <div class="alternative-link">
                    <p>{{ translate('If the button above doesn\'t work, copy and paste this link into your browser:') }}</p>
                    <code>{{ $array['link'] }}</code>
                </div>
            @endif

            <p style="color: #6c757d; font-size: 14px; margin-top: 30px;">
                {{ translate('Need help? Contact our support team at') }}
                <a href="mailto:{{ get_setting('contact_email', 'support@' . request()->getHost()) }}" style="color: #667eea;">
                    {{ get_setting('contact_email', 'support@' . request()->getHost()) }}
                </a>
            </p>
        </div>
        
        <!-- Footer -->
        <div class="email-footer">
            <p class="footer-text">
                {{ translate('Thank you for choosing') }} {{ get_setting('site_name') }}!
            </p>
            
            <div class="social-links">
                <a href="{{ route('home') }}">{{ translate('Visit Website') }}</a>
                <a href="{{ route('contact-us') }}">{{ translate('Contact Support') }}</a>
            </div>
            
            <p class="company-info">
                © {{ date('Y') }} {{ get_setting('site_name') }}. {{ translate('All rights reserved.') }}<br>
                {{ translate('This is an automated email, please do not reply.') }}
            </p>
        </div>
    </div>
</body>
</html>
