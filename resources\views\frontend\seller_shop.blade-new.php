@php
    $theme = get_setting('homepage_select');
@endphp

@extends('frontend.'.$theme.'.layouts.app')

@section('meta_title'){{ $shop->meta_title }}@stop

@section('meta_description'){{ $shop->meta_description }}@stop

@section('meta')
    <!-- Schema.org markup for Google+ -->
    <meta itemprop="name" content="{{ $shop->meta_title }}">
    <meta itemprop="description" content="{{ $shop->meta_description }}">
    <meta itemprop="image" content="{{ uploaded_asset($shop->logo) }}">

    <!-- Twitter Card data -->
    <meta name="twitter:card" content="website">
    <meta name="twitter:site" content="@publisher_handle">
    <meta name="twitter:title" content="{{ $shop->meta_title }}">
    <meta name="twitter:description" content="{{ $shop->meta_description }}">
    <meta name="twitter:creator" content="@author_handle">
    <meta name="twitter:image" content="{{ uploaded_asset($shop->meta_img) }}">

    <!-- Open Graph data -->
    <meta property="og:title" content="{{ $shop->meta_title }}" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="{{ route('shop.visit', $shop->slug) }}" />
    <meta property="og:image" content="{{ uploaded_asset($shop->logo) }}" />
    <meta property="og:description" content="{{ $shop->meta_description }}" />
    <meta property="og:site_name" content="{{ $shop->name }}" />
@endsection

@section('content')
    <section class="mt-4 mb-4" style="background: #fff; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
        <div class="container">
            <!-- Enhanced Top Menu -->
            <div class="d-flex flex-wrap justify-content-center justify-content-md-start py-3">
                <a class="nav-link-enhanced mr-4 @if(!isset($type)) active @endif"
                   href="{{ route('shop.visit', $shop->slug) }}">
                    <i class="las la-home mr-2"></i>{{ translate('Store Home')}}
                </a>
                <a class="nav-link-enhanced mr-4 @if(isset($type) && $type == 'top-selling') active @endif"
                   href="{{ route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'top-selling']) }}">
                    <i class="las la-fire mr-2"></i>{{ translate('Top Selling')}}
                </a>
                <a class="nav-link-enhanced mr-4 @if(isset($type) && $type == 'cupons') active @endif"
                   href="{{ route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'cupons']) }}">
                    <i class="las la-tags mr-2"></i>{{ translate('Coupons')}}
                </a>
                <a class="nav-link-enhanced @if(isset($type) && $type == 'all-products') active @endif"
                   href="{{ route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'all-products']) }}">
                    <i class="las la-th-large mr-2"></i>{{ translate('All Products')}}
                </a>
            </div>
        </div>
    </section>

    <style>
        .nav-link-enhanced {
            display: inline-flex;
            align-items: center;
            padding: 12px 20px;
            text-decoration: none;
            color: #6c757d;
            font-weight: 600;
            font-size: 14px;
            border-radius: 25px;
            transition: all 0.3s ease;
            position: relative;
            background: transparent;
        }

        .nav-link-enhanced:hover {
            color: #007bff;
            background: rgba(0, 123, 255, 0.1);
            text-decoration: none;
            transform: translateY(-1px);
        }

        .nav-link-enhanced.active {
            color: #fff;
            background: linear-gradient(45deg, #007bff, #0056b3);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        .nav-link-enhanced.active:hover {
            color: #fff;
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        /* Enhanced Product Card Styles */
        .enhanced-product-card {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            overflow: hidden;
            position: relative;
            height: 100%;
        }

        .enhanced-product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        .enhanced-product-card .product__items {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .enhanced-product-card .product__items--thumbnail {
            border-radius: 12px 12px 0 0;
            overflow: hidden;
            position: relative;
        }

        .enhanced-product-card .product__items--content {
            padding: 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .enhanced-product-card .product__items--content__title {
            font-size: 16px;
            font-weight: 600;
            line-height: 1.4;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .enhanced-product-card .product__items--content__title a {
            color: inherit;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .enhanced-product-card .product__items--content__title a:hover {
            color: #007bff;
        }

        .enhanced-product-card .product__items--price {
            margin-bottom: 15px;
        }

        .enhanced-product-card .current__price {
            font-size: 18px;
            font-weight: 700;
            color: #e74c3c;
        }

        .enhanced-product-card .old__price {
            font-size: 14px;
            color: #95a5a6;
            text-decoration: line-through;
            margin-left: 8px;
        }

        .enhanced-product-card .product__badge {
            top: 15px;
            left: 15px;
            z-index: 2;
        }

        .enhanced-product-card .product__badge--items {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
        }

        .enhanced-product-card .product__items--action {
            margin-top: auto;
        }

        .enhanced-product-card .product__items--action__btn {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 8px 12px;
            transition: all 0.3s ease;
            color: #6c757d;
        }

        .enhanced-product-card .product__items--action__btn:hover {
            background: #007bff;
            border-color: #007bff;
            color: white;
            transform: translateY(-1px);
        }

        .enhanced-product-card .add__to--cart.active {
            background: #28a745;
            border-color: #28a745;
            color: white;
        }

        /* Enhanced Grid Spacing */
        .enhanced-product-grid .row {
            margin: 0 -15px;
        }

        .enhanced-product-grid .col {
            padding: 0 15px;
            margin-bottom: 30px;
        }

        /* Responsive Improvements */
        @media (max-width: 768px) {
            .enhanced-product-card .product__items--content {
                padding: 15px;
            }

            .enhanced-product-card .product__items--content__title {
                font-size: 14px;
            }

            .enhanced-product-card .current__price {
                font-size: 16px;
            }
        }

        /* Hover Effects for Follow Button */
        .follow-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4) !important;
        }

        .follow-btn.followed:hover {
            background: #dc3545 !important;
            border-color: #dc3545 !important;
        }

        .follow-btn.followed:hover span {
            display: none;
        }

        .follow-btn.followed:hover:after {
            content: 'Unfollow';
        }

        /* Amazon-Style Product Card Enhancements */
        .amazon-style-product {
            background: #fff;
            border: 1px solid #e7e7e7;
            border-radius: 8px;
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
        }

        .amazon-style-product:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transform: translateY(-2px);
            border-color: #007bff;
        }

        .amazon-product-image {
            position: relative;
            overflow: hidden;
        }

        .amazon-product-image:hover .product__items--img {
            transform: scale(1.05);
        }

        .amazon-quick-actions {
            position: absolute;
            top: 12px;
            right: 12px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            opacity: 0;
            transform: translateX(10px);
            transition: all 0.3s ease;
        }

        .amazon-style-product:hover .amazon-quick-actions {
            opacity: 1;
            transform: translateX(0);
        }

        .amazon-quick-btn {
            width: 36px;
            height: 36px;
            background: rgba(255,255,255,0.9);
            border: 1px solid #e7e7e7;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .amazon-quick-btn:hover {
            background: #007bff;
            color: white;
            border-color: #007bff;
            transform: scale(1.1);
        }

        .amazon-discount-badge {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(255,107,107,0.3);
        }

        .amazon-product-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .amazon-category-badge {
            display: inline-block;
            background: #f8f9fa;
            color: #6c757d;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .amazon-product-title {
            font-size: 14px;
            font-weight: 600;
            line-height: 1.4;
            margin-bottom: 8px;
            height: 40px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .amazon-product-link {
            color: #2c3e50;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .amazon-product-link:hover {
            color: #007bff;
            text-decoration: none;
        }

        .amazon-rating-section {
            margin-bottom: 8px;
        }

        .amazon-stars .rating__list--icon {
            color: #ffa500;
            font-size: 14px;
        }

        .amazon-rating-count {
            font-size: 12px;
            color: #666;
            margin-left: 5px;
        }

        .amazon-price-section {
            margin-bottom: 12px;
        }

        .amazon-price-container {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
        }

        .amazon-current-price {
            font-size: 18px;
            font-weight: 700;
            color: #B12704;
        }

        .amazon-original-price {
            font-size: 14px;
            color: #666;
            text-decoration: line-through;
        }

        .amazon-save-amount {
            background: #ff6b6b;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 600;
        }

        .amazon-bid-section {
            display: flex;
            flex-direction: column;
        }

        .amazon-bid-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .amazon-bid-price {
            font-size: 16px;
            font-weight: 700;
            color: #007bff;
        }

        .amazon-add-to-cart-btn {
            width: 100%;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 16px;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .amazon-add-to-cart-btn:hover {
            background: linear-gradient(45deg, #0056b3, #004085);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }

        .amazon-add-to-cart-btn.added {
            background: linear-gradient(45deg, #28a745, #1e7e34);
        }

        .amazon-bid-btn {
            width: 100%;
            background: linear-gradient(45deg, #ffc107, #e0a800);
            color: #212529;
            border: none;
            border-radius: 6px;
            padding: 10px 16px;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .amazon-bid-btn:hover {
            background: linear-gradient(45deg, #e0a800, #d39e00);
            transform: translateY(-1px);
            text-decoration: none;
            color: #212529;
        }

        .amazon-delivery-info {
            border-top: 1px solid #f0f0f0;
            padding-top: 8px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .amazon-product-title {
                font-size: 13px;
                height: 36px;
            }

            .amazon-current-price {
                font-size: 16px;
            }

            .amazon-add-to-cart-btn,
            .amazon-bid-btn {
                padding: 8px 12px;
                font-size: 13px;
            }
        }
    </style>

    @php
        $followed_sellers = [];
        if (Auth::check()) {
            $followed_sellers = get_followed_sellers();
        }
    @endphp

    @if (!isset($type) || $type == 'top-selling' || $type == 'cupons')
        @if ($shop->top_banner)
            <!-- Top Banner -->
            <section class="h-160px h-md-200px h-lg-300px h-xl-100 w-100">
                <img class="d-block lazyload h-100 img-fit"
                     src="{{ static_asset('assets/img/placeholder-rect.jpg') }}"
                     data-src="{{ uploaded_asset($shop->top_banner) }}" alt="{{ env('APP_NAME') }} offer"
                     style="width: 100%;">
            </section>
        @endif
    @endif

    <section class="@if (!isset($type) || $type == 'top-selling' || $type == 'cupons') mb-4 @endif" style="background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border: 1px solid #e9ecef; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
        <div class="container">
            <!-- Enhanced Seller Info -->
            <div class="py-5">
                <div class="row align-items-center">
                    <div class="col-lg-6 col-md-7">
                        <div class="d-flex align-items-center">
                            <!-- Enhanced Shop Logo -->
                            <a href="{{ route('shop.visit', $shop->slug) }}" class="overflow-hidden rounded-circle position-relative" style="width: 80px; height: 80px; border: 3px solid #fff; box-shadow: 0 8px 25px rgba(0,0,0,0.15); background: #fff;">
                                <img class="lazyload w-100 h-100 object-fit-cover"
                                     src="{{ static_asset('assets/img/placeholder.jpg') }}"
                                     data-src="{{ uploaded_asset($shop->logo) }}"
                                     onerror="this.onerror=null;this.src='{{ static_asset('assets/img/placeholder.jpg') }}';"
                                     style="object-fit: cover;">
                                @if ($shop->verification_status == 1)
                                    <div class="position-absolute" style="bottom: -2px; right: -2px; width: 24px; height: 24px; background: #28a745; border-radius: 50%; border: 2px solid #fff; display: flex; align-items: center; justify-content: center;">
                                        <i class="las la-check text-white" style="font-size: 12px;"></i>
                                    </div>
                                @endif
                            </a>
                            <div class="ml-4">
                                <!-- Enhanced Shop Name & Verification Status -->
                                <div class="d-flex align-items-center mb-2">
                                    <a href="{{ route('shop.visit', $shop->slug) }}" class="text-dark fs-20 fw-700 text-decoration-none hover-primary">
                                        {{ $shop->name }}
                                    </a>
                                    @if ($shop->verification_status == 1)
                                        <span class="ml-2 badge badge-success px-2 py-1 fs-11">
                                            <i class="las la-shield-alt mr-1"></i>{{ translate('Verified') }}
                                        </span>
                                    @endif
                                </div>
                                <!-- Enhanced Rating -->
                                <div class="d-flex align-items-center mb-2">
                                    <div class="rating rating-mr-1 text-warning">
                                        {{ renderStarRating($shop->rating) }}
                                    </div>
                                    <span class="ml-2 fs-14 text-muted">
                                        <strong>{{ number_format($shop->rating, 1) }}</strong>
                                        ({{ $shop->num_of_reviews }} {{ translate('Reviews') }})
                                    </span>
                                </div>
                                <!-- Enhanced Address -->
                                <div class="d-flex align-items-center text-muted fs-13">
                                    <i class="las la-map-marker-alt mr-1"></i>
                                    <span>{{ $shop->address }}</span>
                                </div>
                                <!-- Seller Performance Metrics -->
                                <div class="d-flex align-items-center mt-3">
                                    <div class="mr-4">
                                        <div class="fs-12 text-muted">{{ translate('Products') }}</div>
                                        <div class="fs-14 fw-600 text-dark">{{ $shop->user->products->where('published', 1)->count() }}</div>
                                    </div>
                                    <div class="mr-4">
                                        <div class="fs-12 text-muted">{{ translate('Followers') }}</div>
                                        <div class="fs-14 fw-600 text-dark">{{ count($shop->followers) }}</div>
                                    </div>
                                    <div>
                                        <div class="fs-12 text-muted">{{ translate('Member Since') }}</div>
                                        <div class="fs-14 fw-600 text-dark">{{ date('M Y', strtotime($shop->created_at)) }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-5">
                        <div class="d-flex flex-column align-items-lg-end">
                            <!-- Enhanced Social Links -->
                            @if ($shop->facebook || $shop->instagram || $shop->google || $shop->twitter || $shop->youtube)
                                <div class="mb-3">
                                    <div class="fs-13 text-muted mb-2">{{ translate('Connect with us') }}</div>
                                    <div class="d-flex">
                                        @if ($shop->facebook)
                                            <a href="{{ $shop->facebook }}" class="btn btn-light btn-sm rounded-circle mr-2 d-flex align-items-center justify-content-center" target="_blank" style="width: 36px; height: 36px; border: 1px solid #e9ecef;">
                                                <i class="lab la-facebook-f text-primary"></i>
                                            </a>
                                        @endif
                                        @if ($shop->instagram)
                                            <a href="{{ $shop->instagram }}" class="btn btn-light btn-sm rounded-circle mr-2 d-flex align-items-center justify-content-center" target="_blank" style="width: 36px; height: 36px; border: 1px solid #e9ecef;">
                                                <i class="lab la-instagram text-danger"></i>
                                            </a>
                                        @endif
                                        @if ($shop->twitter)
                                            <a href="{{ $shop->twitter }}" class="btn btn-light btn-sm rounded-circle mr-2 d-flex align-items-center justify-content-center" target="_blank" style="width: 36px; height: 36px; border: 1px solid #e9ecef;">
                                                <i class="lab la-twitter text-info"></i>
                                            </a>
                                        @endif
                                        @if ($shop->youtube)
                                            <a href="{{ $shop->youtube }}" class="btn btn-light btn-sm rounded-circle mr-2 d-flex align-items-center justify-content-center" target="_blank" style="width: 36px; height: 36px; border: 1px solid #e9ecef;">
                                                <i class="lab la-youtube text-danger"></i>
                                            </a>
                                        @endif
                                        @if ($shop->google)
                                            <a href="{{ $shop->google }}" class="btn btn-light btn-sm rounded-circle d-flex align-items-center justify-content-center" target="_blank" style="width: 36px; height: 36px; border: 1px solid #e9ecef;">
                                                <i class="lab la-google text-warning"></i>
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            @endif

                            <!-- Enhanced Follow Button -->
                            <div class="mb-3">
                                @if(in_array($shop->id, $followed_sellers))
                                    <a href="{{ route("followed_seller.remove", ['id'=>$shop->id]) }}"
                                       data-toggle="tooltip"
                                       data-title="{{ translate('Unfollow Seller') }}"
                                       data-placement="top"
                                       class="btn btn-outline-success d-flex align-items-center justify-content-center px-4 py-2 follow-btn followed"
                                       style="border-radius: 25px; min-width: 160px; transition: all 0.3s ease;">
                                        <i class="las la-check mr-2"></i>
                                        <span class="fw-600">{{ translate('Following') }}</span>
                                    </a>
                                @else
                                    <a href="{{ route("followed_seller.store", ['id'=>$shop->id]) }}"
                                       class="btn btn-primary d-flex align-items-center justify-content-center px-4 py-2 follow-btn"
                                       style="border-radius: 25px; min-width: 160px; background: linear-gradient(45deg, #007bff, #0056b3); border: none; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0,123,255,0.3);">
                                        <i class="las la-plus mr-2"></i>
                                        <span class="fw-600">{{ translate('Follow Seller') }}</span>
                                    </a>
                                @endif
                            </div>

                            <!-- Enhanced Map Section -->
                            @if(!empty($shop->latitude) && !empty($shop->longitude))
                                <div class="text-center">
                                    <a href="https://www.google.com/maps/search/?api=1&query={{ $shop->latitude }},{{ $shop->longitude }}"
                                       target="_blank"
                                       class="d-inline-block rounded overflow-hidden"
                                       style="border: 2px solid #e9ecef; box-shadow: 0 4px 15px rgba(0,0,0,0.1); transition: all 0.3s ease;">
                                        @if(get_setting('google_map') == 1)
                                            <img src="https://maps.googleapis.com/maps/api/staticmap?center={{ $shop->latitude }},{{ $shop->longitude }}&zoom=15&size=180x120&markers=color:red|{{ $shop->latitude }},{{ $shop->longitude }}&key={{ env('MAP_API_KEY') }}"
                                                 alt="Store Location"
                                                 class="img-fluid"
                                                 style="width: 180px; height: 120px; object-fit: cover;">
                                        @else
                                            <img src="https://static-maps.yandex.ru/1.x/?ll={{ $shop->longitude }},{{ $shop->latitude }}&z=15&size=180,120&l=map&pt={{ $shop->longitude }},{{ $shop->latitude }},pm2rdl&lang=en_US"
                                                 alt="Store Location"
                                                 class="img-fluid"
                                                 style="width: 180px; height: 120px; object-fit: cover;">
                                        @endif
                                    </a>
                                    <div class="fs-12 text-muted mt-2">
                                        <i class="las la-map-marker-alt mr-1"></i>{{ translate('View on Map') }}
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    @if (!isset($type))
        @php
            $feature_products = $shop->user->products->where('published', 1)->where('approved', 1)->where('seller_featured', 1);
        @endphp
        @if (count($feature_products) > 0)
            <!-- Enhanced Featured Products -->
            <section class="mt-4 mb-4" id="section_featured">
                <div class="container">
                    <!-- Enhanced Header -->
                    <div class="d-flex mb-4 align-items-center justify-content-between p-4 rounded-lg" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        <div>
                            <h3 class="fs-20 fs-md-24 fw-700 mb-1 text-white">
                                <i class="las la-star mr-2"></i>{{ translate('Featured Products') }}
                            </h3>
                            <p class="fs-14 mb-0 text-white-50">{{ translate('Handpicked products just for you') }}</p>
                        </div>
                        <div class="d-flex">
                            <button type="button" class="btn btn-light btn-sm rounded-circle mr-2 d-flex align-items-center justify-content-center" onclick="clickToSlide('slick-prev','section_featured')" style="width: 40px; height: 40px;">
                                <i class="las la-angle-left fs-18"></i>
                            </button>
                            <button type="button" class="btn btn-light btn-sm rounded-circle d-flex align-items-center justify-content-center" onclick="clickToSlide('slick-next','section_featured')" style="width: 40px; height: 40px;">
                                <i class="las la-angle-right fs-18"></i>
                            </button>
                        </div>
                    </div>
                    <!-- Enhanced Products Grid -->
                    <div class="enhanced-product-grid">
                        <div class="row row-cols-xl-4 row-cols-lg-3 row-cols-md-3 row-cols-2 g-4">
                            @foreach ($feature_products as $key => $product)
                                <div class="col">
                                    <div class="enhanced-product-card">
                                        @include('frontend.'.get_setting('homepage_select').'.partials.seller_product_box_1',['product' => $product])
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </section>
        @endif

        <!-- Banner Slider -->
        <section class="mt-3 mb-3">
            <div class="container">
                <div class="aiz-carousel mobile-img-auto-height" data-arrows="true" data-dots="false" data-autoplay="true">
                    @if ($shop->sliders != null)
                        @foreach (explode(',',$shop->sliders) as $key => $slide)
                            <div class="carousel-box w-100 h-140px h-md-300px h-xl-450px">
                                <img class="d-block lazyload h-100 img-fit" src="{{ static_asset('assets/img/placeholder-rect.jpg') }}" data-src="{{ uploaded_asset($slide) }}" alt="{{ $key }} offer">
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>
        </section>

        <!-- Coupons -->
        @php
            $coupons = get_coupons($shop->user->id);
        @endphp
        @if (count($coupons)>0)
            <section class="mt-3 mb-3" id="section_coupons">
                <div class="container">
                    <!-- Top Section -->
                    <div class="d-flex mb-4 align-items-baseline justify-content-between">
                        <!-- Title -->
                        <h3 class="fs-16 fs-md-20 fw-700 mb-3 mb-sm-0">
                            <span class="pb-3">{{ translate('Coupons') }}</span>
                        </h3>
                        <!-- Links -->
                        <div class="d-flex">
                            <a type="button" class="arrow-prev slide-arrow link-disable text-secondary mr-2" onclick="clickToSlide('slick-prev','section_coupons')"><i class="las la-angle-left fs-20 fw-600"></i></a>
                            <a class="text-blue fs-12 fw-700 hov-text-primary" href="{{ route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'cupons']) }}">{{ translate('View All') }}</a>
                            <a type="button" class="arrow-next slide-arrow text-secondary ml-2" onclick="clickToSlide('slick-next','section_coupons')"><i class="las la-angle-right fs-20 fw-600"></i></a>
                        </div>
                    </div>
                    <!-- Coupons Section -->
                    <div class="aiz-carousel sm-gutters-16 arrow-none" data-items="3" data-lg-items="2" data-sm-items="1" data-arrows='true' data-infinite='false'>
                        @foreach ($coupons->take(10) as $key => $coupon)
                            <div class="carousel-box">
                                @include('frontend.'.get_setting('homepage_select').'.partials.coupon_box',['coupon' => $coupon])
                            </div>
                        @endforeach
                    </div>
                </div>
            </section>
        @endif

        @if ($shop->banner_full_width_1)
            <!-- Banner full width 1 -->
            @foreach (explode(',',$shop->banner_full_width_1) as $key => $banner)
                <section class="container mb-3 mt-3">
                    <div class="w-100">
                        <img class="d-block lazyload h-100 img-fit"
                             src="{{ static_asset('assets/img/placeholder-rect.jpg') }}"
                             data-src="{{ uploaded_asset($banner) }}" alt="{{ env('APP_NAME') }} offer">
                    </div>
                </section>
            @endforeach
        @endif

        @if($shop->banners_half_width)
            <!-- Banner half width -->
            <section class="container  mb-3 mt-3">
                <div class="row gutters-16">
                    @foreach (explode(',',$shop->banners_half_width) as $key => $banner)
                        <div class="col-md-6 mb-3 mb-md-0">
                            <div class="w-100">
                                <img class="d-block lazyload h-100 img-fit"
                                     src="{{ static_asset('assets/img/placeholder-rect.jpg') }}"
                                     data-src="{{ uploaded_asset($banner) }}" alt="{{ env('APP_NAME') }} offer">
                            </div>
                        </div>
                    @endforeach
                </div>
            </section>
        @endif

    @endif

    <section class="mb-4 mt-4" id="section_types">
        <div class="container">
            <!-- Enhanced Section Header -->
            <div class="d-flex mb-4 align-items-center justify-content-between p-4 rounded-lg" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                <div>
                    <h3 class="fs-20 fs-md-24 fw-700 mb-1 text-white">
                        @if (!isset($type))
                            <i class="las la-sparkles mr-2"></i>{{ translate('New Arrival Products')}}
                        @elseif ($type == 'top-selling')
                            <i class="las la-fire mr-2"></i>{{ translate('Top Selling')}}
                        @elseif ($type == 'cupons')
                            <i class="las la-tags mr-2"></i>{{ translate('All Coupons')}}
                        @endif
                    </h3>
                    <p class="fs-14 mb-0 text-white-50">
                        @if (!isset($type))
                            {{ translate('Discover our latest products') }}
                        @elseif ($type == 'top-selling')
                            {{ translate('Most popular products') }}
                        @elseif ($type == 'cupons')
                            {{ translate('Save more with our exclusive offers') }}
                        @endif
                    </p>
                </div>
                @if (!isset($type))
                    <div class="d-flex">
                        <button type="button" class="btn btn-light btn-sm rounded-circle mr-2 d-flex align-items-center justify-content-center" onclick="clickToSlide('slick-prev','section_types')" style="width: 40px; height: 40px;">
                            <i class="las la-angle-left fs-18"></i>
                        </button>
                        <button type="button" class="btn btn-light btn-sm rounded-circle d-flex align-items-center justify-content-center" onclick="clickToSlide('slick-next','section_types')" style="width: 40px; height: 40px;">
                            <i class="las la-angle-right fs-18"></i>
                        </button>
                    </div>
                @endif
            </div>

            @php
                if (!isset($type)){
                    $products = get_seller_products($shop->user->id);
                }
                elseif ($type == 'top-selling'){
                    $products = get_shop_best_selling_products($shop->user->id);
                }
                elseif ($type == 'cupons'){
                    $coupons = get_coupons($shop->user->id , 24);
                }
            @endphp

            @if (!isset($type))
                <!-- Enhanced New Arrival Products Section -->
                <div class="enhanced-product-grid px-3">
                    <div class="row row-cols-xl-4 row-cols-lg-3 row-cols-md-3 row-cols-2 g-4">
                        @foreach ($products as $key => $product)
                            <div class="col">
                                <div class="enhanced-product-card">
                                    @include('frontend.'.get_setting('homepage_select').'.partials.seller_product_box_1',['product' => $product])
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                @if ($shop->banner_full_width_2)
                    <!-- Banner full width 2 -->
                    @foreach (explode(',',$shop->banner_full_width_2) as $key => $banner)
                        <div class="mt-3 mb-3 w-100">
                            <img class="d-block lazyload h-100 img-fit"
                                 src="{{ static_asset('assets/img/placeholder-rect.jpg') }}"
                                 data-src="{{ uploaded_asset($banner) }}" alt="{{ env('APP_NAME') }} offer">
                        </div>
                    @endforeach
                @endif


            @elseif ($type == 'cupons')
                <!-- All Coupons Section -->
                <div class="row gutters-16 row-cols-xl-3 row-cols-md-2 row-cols-1">
                    @foreach ($coupons as $key => $coupon)
                        <div class="col mb-4">
                            @include('frontend.'.get_setting('homepage_select').'.partials.coupon_box',['coupon' => $coupon])
                        </div>
                    @endforeach
                </div>
                <div class="aiz-pagination mt-4 mb-4">
                    {{ $coupons->links() }}
                </div>

            @elseif ($type == 'all-products')
                <!-- All Products Section -->
                <form class="" id="search-form" action="" method="GET">
                    <div class="row gutters-16 justify-content-center">
                        <!-- Sidebar -->
                        <div class="col-xl-3 col-md-6 col-sm-8">

                            <!-- Sidebar Filters -->
                            <div class="aiz-filter-sidebar collapse-sidebar-wrap sidebar-xl sidebar-right z-1035">
                                <div class="overlay overlay-fixed dark c-pointer" data-toggle="class-toggle" data-target=".aiz-filter-sidebar" data-same=".filter-sidebar-thumb"></div>
                                <div class="collapse-sidebar c-scrollbar-light text-left">
                                    <div class="d-flex d-xl-none justify-content-between align-items-center pl-3 border-bottom">
                                        <h3 class="h6 mb-0 fw-600">{{ translate('Filters') }}</h3>
                                        <button type="button" class="btn btn-sm p-2 filter-sidebar-thumb" data-toggle="class-toggle" data-target=".aiz-filter-sidebar" >
                                            <i class="las la-times la-2x"></i>
                                        </button>
                                    </div>

                                    <!-- Categories -->
                                    <div class="bg-white border mb-4 mx-3 mx-xl-0 mt-3 mt-xl-0">
                                        <div class="fs-16 fw-700 p-3">
                                            <a href="#collapse_1" class="dropdown-toggle filter-section text-dark d-flex align-items-center justify-content-between" data-toggle="collapse">
                                                {{ translate('Categories')}}
                                            </a>
                                        </div>
                                        <div class="collapse show px-3" id="collapse_1">
                                            @foreach (get_categories_by_products($shop->user->id) as $category)
                                                <label class="aiz-checkbox mb-3">
                                                    <input
                                                        type="checkbox"
                                                        name="selected_categories[]"
                                                        value="{{ $category->id }}" @if (in_array($category->id, $selected_categories)) checked @endif
                                                        onchange="filter()"
                                                    >
                                                    <span class="aiz-square-check"></span>
                                                    <span class="fs-14 fw-400 text-dark">{{ $category->getTranslation('name') }}</span>
                                                </label>
                                                <br>
                                            @endforeach
                                        </div>
                                    </div>

                                    <!-- Price range -->
                                    <div class="bg-white border mb-3">
                                        <div class="fs-16 fw-700 p-3">
                                            {{ translate('Price range')}}
                                        </div>
                                        <div class="p-3 mr-3">
                                            <div class="aiz-range-slider">
                                                <div
                                                    id="input-slider-range"
                                                    data-range-value-min="@if(get_products_count($shop->user->id) < 1) 0 @else {{ get_product_min_unit_price($shop->user->id) }} @endif"
                                                    data-range-value-max="@if(get_products_count($shop->user->id) < 1) 0 @else {{ get_product_max_unit_price($shop->user->id) }} @endif"
                                                ></div>

                                                <div class="row mt-2">
                                                    <div class="col-6">
                                                        <span class="range-slider-value value-low fs-14 fw-600 opacity-70"
                                                              @if ($min_price != null)
                                                                  data-range-value-low="{{ $min_price }}"
                                                              @elseif($products->min('unit_price') > 0)
                                                                  data-range-value-low="{{ $products->min('unit_price') }}"
                                                              @else
                                                                  data-range-value-low="0"
                                                              @endif
                                                              id="input-slider-range-value-low"
                                                        ></span>
                                                    </div>
                                                    <div class="col-6 text-right">
                                                        <span class="range-slider-value value-high fs-14 fw-600 opacity-70"
                                                              @if ($max_price != null)
                                                                  data-range-value-high="{{ $max_price }}"
                                                              @elseif($products->max('unit_price') > 0)
                                                                  data-range-value-high="{{ $products->max('unit_price') }}"
                                                              @else
                                                                  data-range-value-high="0"
                                                              @endif
                                                              id="input-slider-range-value-high"
                                                        ></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Hidden Items -->
                                        <input type="hidden" name="min_price" value="">
                                        <input type="hidden" name="max_price" value="">
                                    </div>

                                    <!-- Ratings -->
                                    <div class="bg-white border mb-4 mx-3 mx-xl-0 mt-3 mt-xl-0">
                                        <div class="fs-16 fw-700 p-3">
                                            <a href="#collapse_2" class="dropdown-toggle filter-section text-dark d-flex align-items-center justify-content-between" data-toggle="collapse">
                                                {{ translate('Ratings')}}
                                            </a>
                                        </div>
                                        <div class="collapse show px-3" id="collapse_2">
                                            <label class="aiz-checkbox mb-3">
                                                <input
                                                    type="radio"
                                                    name="rating"
                                                    value="5" @if ($rating==5) checked @endif
                                                    onchange="filter()"
                                                >
                                                <span class="aiz-square-check"></span>
                                                <span class="rating rating-mr-1">{{ renderStarRating(5) }}</span>
                                            </label>
                                            <br>
                                            <label class="aiz-checkbox mb-3">
                                                <input
                                                    type="radio"
                                                    name="rating"
                                                    value="4" @if ($rating==4) checked @endif
                                                    onchange="filter()"
                                                >
                                                <span class="aiz-square-check"></span>
                                                <span class="rating rating-mr-1">{{ renderStarRating(4) }}</span>
                                                <span class="fs-14 fw-400 text-dark">{{ translate('And Up')}}</span>
                                            </label>
                                            <br>
                                            <label class="aiz-checkbox mb-3">
                                                <input
                                                    type="radio"
                                                    name="rating"
                                                    value="3" @if ($rating==3) checked @endif
                                                    onchange="filter()"
                                                >
                                                <span class="aiz-square-check"></span>
                                                <span class="rating rating-mr-1">{{ renderStarRating(3) }}</span>
                                                <span class="fs-14 fw-400 text-dark">{{ translate('And Up')}}</span>
                                            </label>
                                            <br>
                                            <label class="aiz-checkbox mb-3">
                                                <input
                                                    type="radio"
                                                    name="rating"
                                                    value="2" @if ($rating==2) checked @endif
                                                    onchange="filter()"
                                                >
                                                <span class="aiz-square-check"></span>
                                                <span class="rating rating-mr-1">{{ renderStarRating(2) }}</span>
                                                <span class="fs-14 fw-400 text-dark">{{ translate('And Up')}}</span>
                                            </label>
                                            <br>
                                            <label class="aiz-checkbox mb-3">
                                                <input
                                                    type="radio"
                                                    name="rating"
                                                    value="1" @if ($rating==1) checked @endif
                                                    onchange="filter()"
                                                >
                                                <span class="aiz-square-check"></span>
                                                <span class="rating rating-mr-1">{{ renderStarRating(1) }}</span>
                                                <span class="fs-14 fw-400 text-dark">{{ translate('And Up')}}</span>
                                            </label>
                                            <br>
                                        </div>
                                    </div>

                                    <!-- Brands -->
                                    <div class="bg-white border mb-4 mx-3 mx-xl-0 mt-3 mt-xl-0">
                                        <div class="fs-16 fw-700 p-3">
                                            <a href="#collapse_3" class="dropdown-toggle filter-section text-dark d-flex align-items-center justify-content-between" data-toggle="collapse">
                                                {{ translate('Brands')}}
                                            </a>
                                        </div>
                                        <div class="collapse show px-3" id="collapse_3">
                                            <div class="row gutters-10">
                                                @foreach (get_brands_by_products($shop->user->id) as $key => $brand)
                                                    <div class="col-6">
                                                        <label class="aiz-megabox d-block mb-3">
                                                            <input value="{{ $brand->slug }}" type="radio" onchange="filter()"
                                                                   name="brand" @isset($brand_id) @if ($brand_id == $brand->id) checked @endif @endisset>
                                                            <span class="d-block aiz-megabox-elem rounded-0 p-3 border-transparent hov-border-primary">
                                                                <img src="{{ uploaded_asset($brand->logo) }}"
                                                                     class="img-fit mb-2" alt="{{ $brand->getTranslation('name') }}">
                                                                <span class="d-block text-center">
                                                                    <span
                                                                        class="d-block fw-400 fs-14">{{ $brand->getTranslation('name') }}</span>
                                                                </span>
                                                            </span>
                                                        </label>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <!-- Contents -->
                        <div class="col-xl-9">
                            <!-- Top Filters -->
                            <div class="text-left mb-2">
                                <div class="row gutters-5 flex-wrap">
                                    <div class="col-lg col-10">
                                        <h1 class="fs-20 fs-md-24 fw-700 text-dark">
                                            {{ translate('All Products') }}
                                        </h1>
                                    </div>
                                    <div class="col-2 col-lg-auto d-xl-none mb-lg-3 text-right">
                                        <button type="button" class="btn btn-icon p-0" data-toggle="class-toggle" data-target=".aiz-filter-sidebar">
                                            <i class="la la-filter la-2x"></i>
                                        </button>
                                    </div>
                                    <div class="col-6 col-lg-auto mb-3 w-lg-200px">
                                        <select class="form-control form-control-sm aiz-selectpicker rounded-0" name="sort_by" onchange="filter()">
                                            <option value="">{{ translate('Sort by')}}</option>
                                            <option value="newest" @isset($sort_by) @if ($sort_by == 'newest') selected @endif @endisset>{{ translate('Newest')}}</option>
                                            <option value="oldest" @isset($sort_by) @if ($sort_by == 'oldest') selected @endif @endisset>{{ translate('Oldest')}}</option>
                                            <option value="price-asc" @isset($sort_by) @if ($sort_by == 'price-asc') selected @endif @endisset>{{ translate('Price low to high')}}</option>
                                            <option value="price-desc" @isset($sort_by) @if ($sort_by == 'price-desc') selected @endif @endisset>{{ translate('Price high to low')}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Products Grid -->
                            <div class="enhanced-product-grid px-3">
                                <div class="row row-cols-xl-4 row-cols-lg-3 row-cols-md-3 row-cols-2 g-4">
                                    @foreach ($products as $key => $product)
                                        <div class="col">
                                            <div class="enhanced-product-card">
                                                @include('frontend.'.get_setting('homepage_select').'.partials.seller_product_box_1',['product' => $product])
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            <div class="d-flex justify-content-center mt-5">
                                <div class="pagination-wrapper">
                                    {{ $products->appends(request()->input())->links() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            @else
                <!-- Enhanced Top Selling Products Section -->
                <div class="enhanced-product-grid px-3">
                    <div class="row row-cols-xl-5 row-cols-lg-4 row-cols-md-3 row-cols-2 g-4">
                        @foreach ($products as $key => $product)
                            <div class="col">
                                <div class="enhanced-product-card">
                                    @include('frontend.'.get_setting('homepage_select').'.partials.seller_product_box_1',['product' => $product])
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                <div class="d-flex justify-content-center mt-5 mb-4">
                    <div class="pagination-wrapper">
                        {{ $products->links() }}
                    </div>
                </div>
            @endif
        </div>
    </section>

@endsection

@section('script')
    <script type="text/javascript">
        function filter(){
            $('#search-form').submit();
        }

        function rangefilter(arg){
            $('input[name=min_price]').val(arg[0]);
            $('input[name=max_price]').val(arg[1]);
            filter();
        }
    </script>
@endsection
