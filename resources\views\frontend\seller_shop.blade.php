@php
    $theme = get_setting('homepage_select');
@endphp

@extends('frontend.'.$theme.'.layouts.app')

@section('meta_title'){{ $shop->meta_title }}@stop

@section('meta_description'){{ $shop->meta_description }}@stop

@section('meta')
    <!-- Schema.org markup for Google+ -->
    <meta itemprop="name" content="{{ $shop->meta_title }}">
    <meta itemprop="description" content="{{ $shop->meta_description }}">
    <meta itemprop="image" content="{{ uploaded_asset($shop->logo) }}">

    <!-- Twitter Card data -->
    <meta name="twitter:card" content="website">
    <meta name="twitter:site" content="@publisher_handle">
    <meta name="twitter:title" content="{{ $shop->meta_title }}">
    <meta name="twitter:description" content="{{ $shop->meta_description }}">
    <meta name="twitter:creator" content="@author_handle">
    <meta name="twitter:image" content="{{ uploaded_asset($shop->meta_img) }}">

    <!-- Open Graph data -->
    <meta property="og:title" content="{{ $shop->meta_title }}" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="{{ route('shop.visit', $shop->slug) }}" />
    <meta property="og:image" content="{{ uploaded_asset($shop->logo) }}" />
    <meta property="og:description" content="{{ $shop->meta_description }}" />
    <meta property="og:site_name" content="{{ $shop->name }}" />
@endsection

@section('content')

    <!-- Top Banner Section -->
    @if ($shop->top_banner)
        <section class="mb-3">
            <div class="container-fluid px-0">
                <div class="position-relative">
                    <img class="d-block w-100 lazyload"
                         src="{{ static_asset('assets/img/placeholder-rect.jpg') }}"
                         data-src="{{ uploaded_asset($shop->top_banner) }}"
                         alt="{{ $shop->name }} banner">
                    <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, rgba(0,0,0,0.3), transparent);"></div>
                </div>
            </div>
        </section>
    @endif

    <!-- Shop Header Section -->
    <section class="py-5 bg-gradient-light border-bottom shadow-sm">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="d-flex align-items-center">
                        <!-- Shop Logo -->
                        <div class="position-relative mr-4 mr-md-5">
                            <div class="shop-logo-container" style="width: 140px; height: 140px; border-radius: 20px; overflow: hidden; border: 4px solid #ffffff; box-shadow: 0 8px 30px rgba(0,0,0,0.15);">
                                @if($shop->logo)
                                    <img class="lazyload w-100 h-100"
                                         style="object-fit: cover;"
                                         src="{{ static_asset('assets/img/placeholder.jpg') }}"
                                         data-src="{{ uploaded_asset($shop->logo) }}"
                                         alt="{{ $shop->name }}"
                                         onerror="this.onerror=null;this.src='{{ static_asset('assets/img/placeholder.jpg') }}';">
                                @else
                                    @php
                                        $colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'];
                                        $randomColor = $colors[array_rand($colors)];
                                        $firstLetter = strtoupper(substr($shop->name, 0, 1));
                                    @endphp
                                    <div class="d-flex align-items-center justify-content-center w-100 h-100" style="background: linear-gradient(135deg, {{ $randomColor }}, {{ $randomColor }}dd);">
                                        <span style="font-size: 3.5rem; color: white; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">{{ $firstLetter }}</span>
                                    </div>
                                @endif
                            </div>
                            @if ($shop->verification_status == 1)
                                <div class="position-absolute verified-badge" style="bottom: 5px; right: 5px; background: linear-gradient(135deg, #28a745, #20c997); border-radius: 50%; padding: 8px; box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);">
                                    <i class="las la-check text-white" style="font-size: 18px;"></i>
                                </div>
                            @endif
                        </div>

                        <!-- Shop Info -->
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center flex-wrap mx-5">
                                <h1 class="fs-28 fw-800 text-dark mb-0 mr-3" style="line-height: 1.2;">{{ $shop->name }}</h1>
                                @if ($shop->verification_status == 1)
                                    <span class="badge badge-success-soft px-3 py-2 rounded-pill" style="font-size: 12px; font-weight: 600;">
                                        <i class="las la-shield-alt mr-1"></i>{{ translate('Verified Seller') }}
                                    </span>
                                @endif
                            </div>

                            <!-- Social Media Links -->
                            @if($shop->facebook || $shop->instagram || $shop->twitter || $shop->youtube || $shop->google)
                                <div class="social-links">
                                    <span class="text-muted fw-600 mr-3 d-block d-sm-inline mb-2 mb-sm-0">{{ translate('Follow us:') }}</span>
                                    <div class="d-flex align-items-center flex-wrap">
                                        @if($shop->facebook)
                                            <a href="{{ $shop->facebook }}" target="_blank" class="social-link facebook mr-3 mb-2" title="Facebook">
                                                <i class="lab la-facebook-f"></i>
                                            </a>
                                        @endif
                                        @if($shop->instagram)
                                            <a href="{{ $shop->instagram }}" target="_blank" class="social-link instagram mr-3 mb-2" title="Instagram">
                                                <i class="lab la-instagram"></i>
                                            </a>
                                        @endif
                                        @if($shop->twitter)
                                            <a href="{{ $shop->twitter }}" target="_blank" class="social-link twitter mr-3 mb-2" title="Twitter">
                                                <i class="lab la-twitter"></i>
                                            </a>
                                        @endif
                                        @if($shop->youtube)
                                            <a href="{{ $shop->youtube }}" target="_blank" class="social-link youtube mr-3 mb-2" title="YouTube">
                                                <i class="lab la-youtube"></i>
                                            </a>
                                        @endif
                                        @if($shop->google)
                                            <a href="{{ $shop->google }}" target="_blank" class="social-link google mr-3 mb-2" title="Google">
                                                <i class="lab la-google"></i>
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 mt-4 mt-lg-0">
                    <!-- Shop Stats -->
                    <div class="stats-container bg-white rounded-lg shadow-sm p-4">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="stat-item">
                                    <div class="stat-icon mb-2">
                                        <i class="las la-box text-primary" style="font-size: 24px;"></i>
                                    </div>
                                    <h4 class="fs-20 fw-700 text-primary mb-1">{{ $shop->user->products->where('published', 1)->where('approved', 1)->count() }}</h4>
                                    <small class="text-muted fw-600">{{ translate('Products') }}</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item border-left border-right">
                                    <div class="stat-icon mb-2">
                                        <i class="las la-users text-success" style="font-size: 24px;"></i>
                                    </div>
                                    <h4 class="fs-20 fw-700 text-success mb-1">{{ $shop->followers->count() }}</h4>
                                    <small class="text-muted fw-600">{{ translate('Followers') }}</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <div class="stat-icon mb-2">
                                        <i class="las la-star text-warning" style="font-size: 24px;"></i>
                                    </div>
                                    <h4 class="fs-20 fw-700 text-warning mb-1">{{ number_format($shop->user->products->where('published', 1)->where('approved', 1)->avg('rating') ?? 0, 1) }}</h4>
                                    <small class="text-muted fw-600">{{ translate('Rating') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Navigation Menu -->
    <section class="shop-navigation bg-white border-bottom shadow-sm sticky-top" style="z-index: 1020;">
        <div class="container">
            <div class="row align-items-center py-3">
                <div class="col-lg-8">
                    <div class="nav-menu-wrapper">
                        <div class="d-flex flex-wrap align-items-center">
                            <a class="nav-link-shop mx-2 @if(!isset($type)) active @endif"
                               href="{{ route('shop.visit', $shop->slug) }}">
                                <i class="las la-home"></i>
                                <span>{{ translate('Store Home')}}</span>
                            </a>
                            <a class="nav-link-shop mx-2 @if(isset($type) && $type == 'all-products') active @endif"
                               href="{{ route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'all-products']) }}">
                                <i class="las la-th-large"></i>
                                <span>{{ translate('All Products')}}</span>
                            </a>
                            <a class="nav-link-shop mx-2 @if(isset($type) && $type == 'top-selling') active @endif"
                               href="{{ route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'top-selling']) }}">
                                <i class="las la-fire"></i>
                                <span>{{ translate('Top Selling')}}</span>
                            </a>
                            @php
                                $coupons = get_coupons($shop->user->id);
                            @endphp
                            @if (count($coupons) > 0)
                                <a class="nav-link-shop mx-2 @if(isset($type) && $type == 'cupons') active @endif"
                                   href="{{ route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'cupons']) }}">
                                    <i class="las la-tags"></i>
                                    <span>{{ translate('Coupons')}}</span>
                                    <span class="badge badge-primary ml-1">{{ count($coupons) }}</span>
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 text-lg-right mt-3 mt-lg-0">
                    <div class="shop-actions d-flex align-items-center justify-content-lg-end justify-content-center">
                        <button class="btn btn-outline-primary btn-md mr-2" onclick="followShop({{ $shop->id }})">
                            <i class="las la-heart mr-1"></i>{{ translate('Follow') }}
                        </button>
                        <button class="btn btn-outline-secondary btn-md" onclick="shareShop()">
                            <i class="las la-share mr-1"></i>{{ translate('Share') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    @if (!isset($type))
        @php
            $feature_products = $shop->user->products->where('published', 1)->where('approved', 1)->where('seller_featured', 1);
        @endphp
        @if (count($feature_products) > 0)
            <!-- Featured Products -->
            <section class="featured-products-section mt-5 mb-5" id="section_featured">
                <div class="container">
                    <!-- Section Header -->
                    <div class="section-header d-flex align-items-center justify-content-between mb-4">
                        <div class="section-title-wrapper">
                            <h2 class="section-title fs-24 fw-800 text-dark mb-2">
                                <i class="las la-star text-warning mr-2"></i>{{ translate('Featured Products') }}
                            </h2>
                            <p class="section-subtitle text-muted mb-0">{{ translate('Handpicked products from our collection') }}</p>
                        </div>
                        <div class="section-controls d-flex align-items-center">
                            <a href="{{ route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'all-products']) }}" class="btn btn-outline-primary btn-md mr-3">
                                {{ translate('View All') }} <i class="las la-arrow-right ml-1"></i>
                            </a>
                            {{-- <div class="carousel-controls">
                                <button type="button" class="carousel-btn carousel-prev" onclick="clickToSlide('slick-prev','section_featured')">
                                    <i class="las la-angle-left"></i>
                                </button>
                                <button type="button" class="carousel-btn carousel-next" onclick="clickToSlide('slick-next','section_featured')">
                                    <i class="las la-angle-right"></i>
                                </button>
                            </div> --}}
                        </div>
                    </div>

                    <!-- Products Grid -->
                    <div class="products-container bg-white rounded-lg shadow-sm p-4">
                        <div class="row row-cols-xl-4 row-cols-lg-3 row-cols-md-2 row-cols-2 g-3">
                            @foreach ($feature_products as $key => $product)
                                <div class="col">
                                    <div class="product-card-wrapper h-100">
                                        <div class="product-card h-100 border rounded-lg overflow-hidden position-relative">
                                            @if($product->discount > 0)
                                                <div class="product-badge position-absolute" style="top: 10px; left: 10px; z-index: 10;">
                                                    <span class="badge badge-danger">-{{ $product->discount }}%</span>
                                                </div>
                                            @endif
                                            @include('frontend.'.get_setting('homepage_select').'.partials.product_box_1',['product' => $product])
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </section>
        @endif

        <!-- Banner Slider -->
        @if ($shop->sliders != null)
            <section class="banner-slider-section mt-4 mb-5">
                <div class="container">
                    <div class="banner-slider-wrapper position-relative">
                        <div class="aiz-carousel mobile-img-auto-height" data-arrows="true" data-dots="true" data-autoplay="true" data-infinite="true" data-speed="800">
                            @foreach (explode(',',$shop->sliders) as $key => $slide)
                                <div class="carousel-box">
                                    <div class="banner-slide position-relative overflow-hidden rounded-lg">
                                        <img class="d-block lazyload w-100"
                                             style="height: 350px; object-fit: cover;"
                                             src="{{ static_asset('assets/img/placeholder-rect.jpg') }}"
                                             data-src="{{ uploaded_asset($slide) }}"
                                             alt="{{ $shop->name }} slider {{ $key + 1 }}">
                                        <div class="banner-overlay position-absolute w-100 h-100" style="top: 0; left: 0; background: linear-gradient(135deg, rgba(0,0,0,0.1), transparent);"></div>
                                        <div class="banner-content position-absolute" style="bottom: 30px; left: 30px; right: 30px;">
                                            <div class="bg-white bg-opacity-90 rounded p-3 d-inline-block">
                                                <h5 class="mb-1 fw-700 text-dark">{{ $shop->name }}</h5>
                                                <p class="mb-0 text-muted small">{{ translate('Exclusive Collection') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </section>
        @endif

        <!-- Coupons -->
        @if (count($coupons)>0)
            <section class="mt-4 mb-4" id="section_coupons">
                <div class="container">
                    <!-- Top Section -->
                    <div class="d-flex mb-4 align-items-baseline justify-content-between">
                        <!-- Title -->
                        <h3 class="fs-20 fw-700 mb-3 mb-sm-0">
                            <span class="text-primary">{{ translate('Available Coupons') }}</span>
                        </h3>
                        <!-- Links -->
                        <div class="d-flex align-items-center">
                            <a type="button" class="arrow-prev slide-arrow text-secondary mr-2" onclick="clickToSlide('slick-prev','section_coupons')"><i class="las la-angle-left fs-20 fw-600"></i></a>
                            <a class="btn btn-md btn-outline-primary mr-2" href="{{ route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'cupons']) }}">{{ translate('View All') }}</a>
                            <a type="button" class="arrow-next slide-arrow text-secondary ml-2" onclick="clickToSlide('slick-next','section_coupons')"><i class="las la-angle-right fs-20 fw-600"></i></a>
                        </div>
                    </div>
                    <!-- Coupons Section -->
                    <div class="bg-white rounded shadow-sm p-3">
                        <div class="aiz-carousel sm-gutters-16" data-items="3" data-lg-items="2" data-sm-items="1" data-arrows='true' data-infinite='false'>
                            @foreach ($coupons->take(10) as $key => $coupon)
                                <div class="carousel-box px-2">
                                    <div class="coupon-card">
                                        @include('frontend.'.get_setting('homepage_select').'.partials.coupon_box',['coupon' => $coupon])
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </section>
        @endif

        @if ($shop->banner_full_width_1)
            <!-- Banner full width 1 -->
            <section class="promotional-banners-section mt-5 mb-5">
                <div class="container">
                    <div class="banners-grid">
                        @foreach (explode(',',$shop->banner_full_width_1) as $key => $banner)
                            <div class="banner-item mb-4">
                                <div class="promotional-banner position-relative overflow-hidden rounded-lg shadow-lg">
                                    <img class="d-block lazyload w-100 banner-image"
                                         style="height: 220px; object-fit: cover; transition: transform 0.5s ease;"
                                         src="{{ static_asset('assets/img/placeholder-rect.jpg') }}"
                                         data-src="{{ uploaded_asset($banner) }}"
                                         alt="{{ $shop->name }} promotional banner {{ $key + 1 }}">
                                    <div class="banner-overlay position-absolute w-100 h-100" style="top: 0; left: 0; background: linear-gradient(135deg, rgba(0,0,0,0.1), transparent);"></div>
                                    <div class="banner-hover-effect position-absolute w-100 h-100" style="top: 0; left: 0; background: rgba(0,0,0,0.05); opacity: 0; transition: opacity 0.3s ease;"></div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </section>
        @endif

        @if($shop->banners_half_width)
            <!-- Banner half width -->
            <section class="dual-banners-section mt-5 mb-5">
                <div class="container">
                    <div class="row g-4">
                        @foreach (explode(',',$shop->banners_half_width) as $key => $banner)
                            <div class="col-md-6">
                                <div class="dual-banner position-relative overflow-hidden rounded-lg shadow-lg">
                                    <img class="d-block lazyload w-100 banner-image"
                                         style="height: 200px; object-fit: cover; transition: transform 0.5s ease;"
                                         src="{{ static_asset('assets/img/placeholder-rect.jpg') }}"
                                         data-src="{{ uploaded_asset($banner) }}"
                                         alt="{{ $shop->name }} banner {{ $key + 1 }}">
                                    <div class="banner-overlay position-absolute w-100 h-100" style="top: 0; left: 0; background: linear-gradient(135deg, rgba(0,0,0,0.1), transparent);"></div>
                                    <div class="banner-hover-effect position-absolute w-100 h-100" style="top: 0; left: 0; background: rgba(0,0,0,0.05); opacity: 0; transition: opacity 0.3s ease;"></div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </section>
        @endif

    @endif

    <section class="main-products-section mt-5 mb-5" id="section_types">
        <div class="container">
            <!-- Section Header -->
            <div class="section-header d-flex align-items-center justify-content-between mb-4">
                <div class="section-title-wrapper">
                    <h2 class="section-title fs-24 fw-800 text-dark mb-2">
                        @if (!isset($type))
                            <i class="las la-box text-primary mr-2"></i>{{ translate('Latest Products')}}
                        @elseif ($type == 'top-selling')
                            <i class="las la-fire text-danger mr-2"></i>{{ translate('Top Selling Products')}}
                        @elseif ($type == 'cupons')
                            <i class="las la-tags text-success mr-2"></i>{{ translate('All Available Coupons')}}
                        @endif
                    </h2>
                    <p class="section-subtitle text-muted mb-0">
                        @if (!isset($type))
                            {{ translate('Discover our newest arrivals') }}
                        @elseif ($type == 'top-selling')
                            {{ translate('Most popular products in our store') }}
                        @elseif ($type == 'cupons')
                            {{ translate('Save more with these exclusive offers') }}
                        @endif
                    </p>
                </div>
                @if (!isset($type))
                    <div class="section-controls d-flex align-items-center">
                        <a class="btn btn-outline-primary btn-md mr-3" href="{{ route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'all-products']) }}">
                            {{ translate('View All Products') }} <i class="las la-arrow-right ml-1"></i>
                        </a>
                        {{-- <div class="carousel-controls">
                            <button type="button" class="carousel-btn carousel-prev" onclick="clickToSlide('slick-prev','section_types')">
                                <i class="las la-angle-left"></i>
                            </button>
                            <button type="button" class="carousel-btn carousel-next" onclick="clickToSlide('slick-next','section_types')">
                                <i class="las la-angle-right"></i>
                            </button>
                        </div> --}}
                    </div>
                @endif
            </div>



            @if (!isset($type))
                <!-- Latest Products Section -->
                <div class="bg-white rounded shadow-sm p-3">
                    <div class="row row-cols-xl-4 row-cols-lg-3 row-cols-md-3 row-cols-2">
                        @foreach ($products as $key => $product)
                            <div class="col mb-3">
                                <div class="product-card h-100">
                                    @include('frontend.'.get_setting('homepage_select').'.partials.product_box_1',['product' => $product])
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Pagination for default products -->
                <div class="aiz-pagination mt-4 mb-4">
                    {{ $products->links() }}
                </div>

                @if ($shop->banner_full_width_2)
                    <!-- Banner full width 2 -->
                    <section class="mt-4 mb-4">
                        <div class="container">
                            @foreach (explode(',',$shop->banner_full_width_2) as $key => $banner)
                                <div class="bg-white rounded shadow-sm overflow-hidden mb-3">
                                    <div class="position-relative">
                                        <img class="d-block lazyload w-100"
                                             style="height: 200px; object-fit: cover;"
                                             src="{{ static_asset('assets/img/placeholder-rect.jpg') }}"
                                             data-src="{{ uploaded_asset($banner) }}"
                                             alt="{{ $shop->name }} promotional banner {{ $key + 1 }}">
                                        <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, rgba(0,0,0,0.1), transparent);"></div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </section>
                @endif


            @elseif ($type == 'cupons')
                <!-- All Coupons Section -->
                <div class="bg-white rounded shadow-sm p-3">
                    <div class="row row-cols-xl-3 row-cols-md-2 row-cols-1">
                        @foreach ($coupons as $key => $coupon)
                            <div class="col mb-4">
                                <div class="coupon-card h-100">
                                    @include('frontend.'.get_setting('homepage_select').'.partials.coupon_box',['coupon' => $coupon])
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                <div class="aiz-pagination mt-4 mb-4">
                    {{ $coupons->links() }}
                </div>

            @elseif ($type == 'all-products')
                <!-- All Products Section -->
                <form class="" id="search-form" action="" method="GET">
                    <div class="row">
                        <!-- Sidebar Filters -->
                        <div class="col-xl-3">
                            <div class="aiz-filter-sidebar collapse-sidebar-wrap sidebar-xl sidebar-right z-1035">
                                <div class="overlay overlay-fixed dark c-pointer" data-toggle="class-toggle" data-target=".aiz-filter-sidebar" data-same=".filter-sidebar-thumb"></div>
                                <div class="collapse-sidebar c-scrollbar-light text-left">
                                    <div class="d-flex d-xl-none justify-content-between align-items-center pl-3 border-bottom">
                                        <h3 class="h6 mb-0 fw-600">{{ translate('Filters') }}</h3>
                                        <button type="button" class="btn btn-md p-2 filter-sidebar-thumb" data-toggle="class-toggle" data-target=".aiz-filter-sidebar" >
                                            <i class="las la-times la-2x"></i>
                                        </button>
                                    </div>

                                    <!-- Categories -->
                                    <div class="bg-white border mb-3">
                                        <div class="fs-16 fw-700 p-3">
                                            <a href="#collapse_1" class="dropdown-toggle filter-section text-dark d-flex align-items-center justify-content-between" data-toggle="collapse">
                                                {{ translate('Categories')}}
                                            </a>
                                        </div>
                                        <div class="collapse show" id="collapse_1">
                                            <ul class="p-3 mb-0 list-unstyled">
                                                @foreach (get_categories_by_products($shop->user->id) as $category)
                                                    <li class="mb-3 text-dark">
                                                        <label class="aiz-checkbox">
                                                            <input
                                                                type="checkbox"
                                                                name="selected_categories[]"
                                                                value="{{ $category->id }}" @if (in_array($category->id, $selected_categories)) checked @endif
                                                                onchange="filter()"
                                                            >
                                                            <span class="aiz-square-check"></span>
                                                            <span class="fs-14 fw-400 text-dark">{{ $category->getTranslation('name') }}</span>
                                                        </label>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    </div>

                                    <!-- Price range -->
                                    <div class="bg-white border mb-3">
                                        <div class="fs-16 fw-700 p-3">
                                            {{ translate('Price range')}}
                                        </div>
                                        <div class="p-3 mr-3">
                                            <div class="aiz-range-slider">
                                                <div
                                                    id="input-slider-range"
                                                    data-range-value-min="@if(get_products_count($shop->user->id) < 1) 0 @else {{ get_product_min_unit_price($shop->user->id) }} @endif"
                                                    data-range-value-max="@if(get_products_count($shop->user->id) < 1) 0 @else {{ get_product_max_unit_price($shop->user->id) }} @endif"
                                                ></div>

                                                <div class="row mt-2">
                                                    <div class="col-6">
                                                        <span class="range-slider-value value-low fs-14 fw-600 opacity-70"
                                                              @if ($min_price != null)
                                                                  data-range-value-low="{{ $min_price }}"
                                                              @elseif($products->min('unit_price') > 0)
                                                                  data-range-value-low="{{ $products->min('unit_price') }}"
                                                              @else
                                                                  data-range-value-low="0"
                                                              @endif
                                                              id="input-slider-range-value-low"
                                                        ></span>
                                                    </div>
                                                    <div class="col-6 text-right">
                                                        <span class="range-slider-value value-high fs-14 fw-600 opacity-70"
                                                              @if ($max_price != null)
                                                                  data-range-value-high="{{ $max_price }}"
                                                              @elseif($products->max('unit_price') > 0)
                                                                  data-range-value-high="{{ $products->max('unit_price') }}"
                                                              @else
                                                                  data-range-value-high="0"
                                                              @endif
                                                              id="input-slider-range-value-high"
                                                        ></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Hidden Items -->
                                        <input type="hidden" name="min_price" value="">
                                        <input type="hidden" name="max_price" value="">
                                    </div>

                                    <!-- Ratings -->
                                    <div class="bg-white border mb-3">
                                        <div class="fs-16 fw-700 p-3">
                                            <a href="#collapse_2" class="dropdown-toggle filter-section text-dark d-flex align-items-center justify-content-between collapsed" data-toggle="collapse" data-target="#collapse_2">
                                                {{ translate('Ratings')}}
                                            </a>
                                        </div>
                                        <div class="collapse" id="collapse_2">
                                            <div class="p-3 aiz-checkbox-list">
                                                <label class="aiz-checkbox mb-3">
                                                    <input
                                                        type="radio"
                                                        name="rating"
                                                        value="5" @if ($rating==5) checked @endif
                                                        onchange="filter()"
                                                    >
                                                    <span class="aiz-square-check"></span>
                                                    <span class="rating rating-mr-1">{{ renderStarRating(5) }}</span>
                                                </label>
                                                <label class="aiz-checkbox mb-3">
                                                    <input
                                                        type="radio"
                                                        name="rating"
                                                        value="4" @if ($rating==4) checked @endif
                                                        onchange="filter()"
                                                    >
                                                    <span class="aiz-square-check"></span>
                                                    <span class="rating rating-mr-1">{{ renderStarRating(4) }}</span>
                                                    <span class="fs-14 fw-400 text-dark">{{ translate('And Up')}}</span>
                                                </label>
                                                <label class="aiz-checkbox mb-3">
                                                    <input
                                                        type="radio"
                                                        name="rating"
                                                        value="3" @if ($rating==3) checked @endif
                                                        onchange="filter()"
                                                    >
                                                    <span class="aiz-square-check"></span>
                                                    <span class="rating rating-mr-1">{{ renderStarRating(3) }}</span>
                                                    <span class="fs-14 fw-400 text-dark">{{ translate('And Up')}}</span>
                                                </label>
                                                <label class="aiz-checkbox mb-3">
                                                    <input
                                                        type="radio"
                                                        name="rating"
                                                        value="2" @if ($rating==2) checked @endif
                                                        onchange="filter()"
                                                    >
                                                    <span class="aiz-square-check"></span>
                                                    <span class="rating rating-mr-1">{{ renderStarRating(2) }}</span>
                                                    <span class="fs-14 fw-400 text-dark">{{ translate('And Up')}}</span>
                                                </label>
                                                <label class="aiz-checkbox mb-3">
                                                    <input
                                                        type="radio"
                                                        name="rating"
                                                        value="1" @if ($rating==1) checked @endif
                                                        onchange="filter()"
                                                    >
                                                    <span class="aiz-square-check"></span>
                                                    <span class="rating rating-mr-1">{{ renderStarRating(1) }}</span>
                                                    <span class="fs-14 fw-400 text-dark">{{ translate('And Up')}}</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Brands -->
                                    <div class="bg-white border mb-3">
                                        <div class="fs-16 fw-700 p-3">
                                            <a href="#collapse_3" class="dropdown-toggle filter-section text-dark d-flex align-items-center justify-content-between collapsed" data-toggle="collapse" data-target="#collapse_3">
                                                {{ translate('Brands')}}
                                            </a>
                                        </div>
                                        <div class="collapse" id="collapse_3">
                                            <div class="p-3 aiz-checkbox-list">
                                                @foreach (get_brands_by_products($shop->user->id) as $key => $brand)
                                                    <label class="aiz-checkbox mb-3">
                                                        <input value="{{ $brand->slug }}" type="radio" onchange="filter()"
                                                               name="brand" @isset($brand_id) @if ($brand_id == $brand->id) checked @endif @endisset>
                                                        <span class="aiz-square-check"></span>
                                                        <span class="fs-14 fw-400 text-dark">{{ $brand->getTranslation('name') }}</span>
                                                    </label>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <!-- Contents -->
                        <div class="col-xl-9">

                            <!-- Breadcrumb -->
                            <ul class="breadcrumb bg-transparent py-0 px-1">
                                <li class="breadcrumb-item has-transition opacity-50 hov-opacity-100">
                                    <a class="text-reset" href="{{ route('home') }}">{{ translate('Home')}}</a>
                                </li>
                                <li class="breadcrumb-item opacity-50 hov-opacity-100">
                                    <a class="text-reset" href="{{ route('shop.visit', $shop->slug) }}">{{ $shop->name }}</a>
                                </li>
                                <li class="text-dark fw-600 breadcrumb-item">
                                    "{{ translate('All Products') }}"
                                </li>
                            </ul>

                            <!-- Top Filters -->
                            <div class="text-left">
                                <div class="row gutters-5 flex-wrap align-items-center">
                                    <div class="col-lg col-10">
                                        <h1 class="fs-20 fs-md-24 fw-700 text-dark">
                                            {{ translate('All Products') }}
                                        </h1>
                                    </div>
                                    <div class="col-2 col-lg-auto d-xl-none mb-lg-3 text-right">
                                        <button type="button" class="btn btn-icon p-0" data-toggle="class-toggle" data-target=".aiz-filter-sidebar">
                                            <i class="la la-filter la-2x"></i>
                                        </button>
                                    </div>
                                    <div class="col-6 col-lg-auto mb-3 w-lg-200px">
                                        <select class="form-control form-control-sm aiz-selectpicker rounded-0" name="sort_by" onchange="filter()">
                                            <option value="">{{ translate('Sort by')}}</option>
                                            <option value="newest" @isset($sort_by) @if ($sort_by == 'newest') selected @endif @endisset>{{ translate('Newest')}}</option>
                                            <option value="oldest" @isset($sort_by) @if ($sort_by == 'oldest') selected @endif @endisset>{{ translate('Oldest')}}</option>
                                            <option value="price-asc" @isset($sort_by) @if ($sort_by == 'price-asc') selected @endif @endisset>{{ translate('Price low to high')}}</option>
                                            <option value="price-desc" @isset($sort_by) @if ($sort_by == 'price-desc') selected @endif @endisset>{{ translate('Price high to low')}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Products -->
                            <div class="px-3">
                                <div class="row gutters-16 row-cols-xxl-4 row-cols-xl-3 row-cols-lg-4 row-cols-md-3 row-cols-2 border-top border-left">
                                    @foreach ($products as $key => $product)
                                        <div class="col border-right border-bottom has-transition hov-shadow-out z-1">
                                            @include('frontend.'.get_setting('homepage_select').'.partials.product_box_1',['product' => $product])
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            <div class="aiz-pagination mt-4">
                                {{ $products->appends(request()->input())->links() }}
                            </div>
                        </div>
                    </div>
                </form>
            @else
                <!-- Top Selling Products Section -->
                <div class="px-3">
                    <div class="row gutters-16 row-cols-xxl-6 row-cols-xl-5 row-cols-lg-4 row-cols-md-3 row-cols-2 border-left border-top">
                        @foreach ($products as $key => $product)
                            <div class="col border-bottom border-right overflow-hidden has-transition hov-shadow-out z-1">
                                @include('frontend.'.get_setting('homepage_select').'.partials.product_box_1',['product' => $product])
                            </div>
                        @endforeach
                    </div>
                </div>
                <div class="aiz-pagination mt-4 mb-4">
                    {{ $products->links() }}
                </div>
            @endif
        </div>
    </section>

@endsection

@section('css')
<style>
    /* Background Gradients */
    .bg-gradient-light {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    /* Shop Header Styles */
    .shop-logo-container {
        transition: all 0.4s ease;
        position: relative;
    }

    .shop-logo-container:hover {
        transform: scale(1.08) rotate(2deg);
        box-shadow: 0 12px 40px rgba(0,0,0,0.2) !important;
    }

    .verified-badge {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    .social-link {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        text-decoration: none;
        transition: all 0.3s ease;
        font-size: 18px;
    }

    .social-link.facebook { background: #3b5998; color: white; }
    .social-link.instagram { background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%); color: white; }
    .social-link.twitter { background: #1da1f2; color: white; }
    .social-link.youtube { background: #ff0000; color: white; }
    .social-link.google { background: #dd4b39; color: white; }

    .social-link:hover {
        transform: translateY(-3px) scale(1.1);
        box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        color: white;
        text-decoration: none;
    }

    .stats-container {
        border: 1px solid #e9ecef;
    }

    .stat-item {
        transition: transform 0.3s ease;
    }

    .stat-item:hover {
        transform: translateY(-5px);
    }

    /* Navigation Styles */
    .shop-navigation {
        backdrop-filter: blur(10px);
        background-color: rgba(255, 255, 255, 0.95) !important;
    }

    .nav-link-shop {
        display: inline-flex;
        align-items: center;
        color: #6c757d;
        text-decoration: none;
        padding: 12px 20px;
        margin-right: 8px;
        border-radius: 30px;
        transition: all 0.3s ease;
        font-weight: 600;
        position: relative;
        overflow: hidden;
    }

    .nav-link-shop i {
        margin-right: 8px;
        font-size: 18px;
    }

    .nav-link-shop:hover {
        color: #007bff;
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,123,255,0.2);
    }

    .nav-link-shop.active {
        color: white;
        background: linear-gradient(135deg, #007bff, #0056b3);
        box-shadow: 0 4px 15px rgba(0,123,255,0.3);
    }

    .nav-link-shop.active:hover {
        color: white;
        background: linear-gradient(135deg, #0056b3, #004085);
    }

    /* Section Headers */
    .section-header {
        margin-bottom: 2rem;
    }

    .section-title {
        position: relative;
        display: inline-block;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 50px;
        height: 3px;
        background: linear-gradient(135deg, #007bff, #0056b3);
        border-radius: 2px;
    }

    /* Carousel Controls */
    .carousel-controls {
        display: flex;
        gap: 8px;
    }

    .carousel-btn {
        width: 40px;
        height: 40px;
        border: 2px solid #007bff;
        background: white;
        color: #007bff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        font-size: 16px;
    }

    .carousel-btn:hover {
        background: #007bff;
        color: white;
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(0,123,255,0.3);
    }

    /* Product Cards */
    .product-card {
        transition: all 0.4s ease;
        border: 1px solid #e9ecef !important;
        position: relative;
        overflow: hidden;
    }

    .product-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: left 0.5s ease;
        z-index: 1;
    }

    .product-card:hover::before {
        left: 100%;
    }

    .product-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        border-color: #007bff !important;
    }

    .product-badge {
        z-index: 10;
    }

    /* Banner Styles */
    .banner-slide:hover .banner-image,
    .promotional-banner:hover .banner-image,
    .dual-banner:hover .banner-image {
        transform: scale(1.05);
    }

    .promotional-banner:hover .banner-hover-effect,
    .dual-banner:hover .banner-hover-effect {
        opacity: 1;
    }

    /* Carousel Enhancements */
    .aiz-carousel .slick-arrow {
        background: linear-gradient(135deg, #007bff, #0056b3);
        border-radius: 50%;
        width: 45px;
        height: 45px;
        z-index: 10;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0,123,255,0.3);
    }

    .aiz-carousel .slick-arrow:hover {
        background: linear-gradient(135deg, #0056b3, #004085);
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(0,123,255,0.4);
    }

    .aiz-carousel .slick-arrow:before {
        color: white;
        font-size: 18px;
        font-weight: bold;
    }

    .aiz-carousel .slick-dots {
        bottom: -50px;
    }

    .aiz-carousel .slick-dots li button:before {
        color: #007bff;
        font-size: 14px;
        opacity: 0.5;
        transition: all 0.3s ease;
    }

    .aiz-carousel .slick-dots li.slick-active button:before,
    .aiz-carousel .slick-dots li:hover button:before {
        color: #007bff;
        opacity: 1;
        transform: scale(1.2);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .shop-logo-container {
            width: 100px !important;
            height: 100px !important;
        }

        .nav-link-shop {
            padding: 10px 15px;
            font-size: 14px;
            margin-right: 5px;
            margin-bottom: 8px;
        }

        .nav-link-shop i {
            margin-right: 5px;
            font-size: 16px;
        }

        .section-title {
            font-size: 20px !important;
        }

        .carousel-controls {
            display: none;
        }

        .social-link {
            width: 35px;
            height: 35px;
            font-size: 16px;
        }

        .stats-container {
            margin-top: 20px;
        }
    }

    @media (max-width: 576px) {
        .shop-logo-container {
            width: 80px !important;
            height: 80px !important;
        }

        .nav-link-shop span {
            display: none;
        }

        .nav-link-shop {
            padding: 10px;
        }

        .section-header {
            flex-direction: column;
            align-items: flex-start !important;
        }

        .section-controls {
            margin-top: 15px;
        }
    }

    /* Loading Animation */
    .lazyload {
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .lazyload.loaded {
        opacity: 1;
    }

    /* Utility Classes */
    .rounded-lg {
        border-radius: 12px !important;
    }

    .badge-success-soft {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .bg-opacity-90 {
        background-color: rgba(255, 255, 255, 0.9) !important;
    }

    .g-3 > * {
        padding: 0.75rem;
    }
</style>
@endsection

@section('script')
    <script type="text/javascript">
        function filter(){
            $('#search-form').submit();
        }

        function rangefilter(arg){
            $('input[name=min_price]').val(arg[0]);
            $('input[name=max_price]').val(arg[1]);
            filter();
        }

        // Enhanced functionality
        $(document).ready(function() {
            // Add loading animation for images
            $('.lazyload').on('load', function() {
                $(this).addClass('loaded');
            });

            // Smooth scroll for navigation
            $('.nav-link-shop').on('click', function(e) {
                if (this.hash !== "") {
                    e.preventDefault();
                    var hash = this.hash;
                    $('html, body').animate({
                        scrollTop: $(hash).offset().top - 100
                    }, 800);
                }
            });

            // Sticky navigation enhancement
            $(window).scroll(function() {
                var scroll = $(window).scrollTop();
                if (scroll >= 100) {
                    $('.shop-navigation').addClass('scrolled');
                } else {
                    $('.shop-navigation').removeClass('scrolled');
                }
            });

            // Product card hover effects
            $('.product-card').hover(
                function() {
                    $(this).find('img').addClass('hovered');
                },
                function() {
                    $(this).find('img').removeClass('hovered');
                }
            );

            // Banner hover effects
            $('.promotional-banner, .dual-banner').hover(
                function() {
                    $(this).find('.banner-hover-effect').css('opacity', '1');
                },
                function() {
                    $(this).find('.banner-hover-effect').css('opacity', '0');
                }
            );
        });

        // Follow shop functionality
        function followShop(shopId) {
            // Add your follow shop logic here
            console.log('Following shop:', shopId);
            // You can implement AJAX call to follow/unfollow shop
        }

        // Share shop functionality
        function shareShop() {
            if (navigator.share) {
                navigator.share({
                    title: '{{ $shop->name }}',
                    text: 'Check out this amazing shop!',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                var url = window.location.href;
                navigator.clipboard.writeText(url).then(function() {
                    alert('Shop link copied to clipboard!');
                });
            }
        }
    </script>
@endsection
