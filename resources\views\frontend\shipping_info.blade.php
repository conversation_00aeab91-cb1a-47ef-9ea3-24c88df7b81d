@php
    $theme = get_setting('homepage_select');
@endphp

@extends('frontend.'.$theme.'.layouts.app')

@section('content')

    <!-- Shipping Info -->
    <section class="py-4 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <form class="form-default" action="{{ route('checkout.store_shipping_infostore') }}" method="POST">
                        @csrf
                        @if(Auth::check())
                            <div class="border bg-white p-4 rounded-3 shadow-sm mb-4">
                                @if(Auth::user()->addresses->count() > 0)
                                    @foreach (Auth::user()->addresses as $key => $address)
                                    <div class="border mb-3 p-3 rounded-3">
                                        <div class="row">

                                            <div class="col">
                                                <!-- Edit Address Button -->
                                                <div class="col text-end">
                                                    <a class="btn btn-primary btn-sm" onclick="edit_address('{{$address->id}}')">{{ translate('Change') }}</a>
                                                </div>

                                                <label class="d-block bg-white mb-3 p-3 border rounded shadow-sm">
                                                    <div class="row">
                                                        <div class="col-1">
                                                            <input type="radio" name="address_id" value="{{ $address->id }}" @if ($address->set_default) checked @endif required class="form-check-input me-2">
                                                        </div>
                                                        <div class="col-11">
                                                            <div class="d-flex align-items-start">
                                                                <!-- Radio Button -->
                                                                {{--                                                        <div class="me-3">--}}
                                                                {{--                                                            <span class="border rounded-circle p-2 d-inline-block"></span>--}}
                                                                {{--                                                        </div>--}}
                                                                <!-- Address Details -->
                                                                <div class="product__details--info__meta">
                                                                    <p class="product__details--info__meta--list"><strong>{{ translate('Address') }} :</strong>  <span>{{ $address->address }}</span> </p>
                                                                    <p class="product__details--info__meta--list"><strong>{{ translate('Postal Code') }}:</strong>  <span>{{ $address->postal_code }}</span> </p>
                                                                    <p class="product__details--info__meta--list"><strong>{{ translate('City') }}:</strong>  <span>{{ optional($address->city)->name }}</span> </p>
                                                                    <p class="product__details--info__meta--list"><strong>{{ translate('State') }}:</strong>  <span>{{ optional($address->state)->name }}</span> </p>
                                                                    <p class="product__details--info__meta--list"><strong>{{ translate('Country') }}:</strong>  <span>{{ optional($address->country)->name }}</span> </p>
                                                                    <p class="product__details--info__meta--list"><strong>{{ translate('Phone') }}:</strong>  <span>{{ $address->phone }}</span> </p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </label>
                                            </div>


                                        </div>
                                    </div>
                                    @endforeach
                                @else
                                    <!-- No addresses message -->
                                    <div class="text-center mb-4">
                                        <div class="alert alert-info">
                                            <i class="las la-info-circle la-2x mb-2"></i>
                                            <h5>{{ translate('No shipping address found') }}</h5>
                                            <p>{{ translate('Please add a shipping address to continue with your order.') }}</p>
                                        </div>
                                    </div>
                                @endif

                                <input type="hidden" name="checkout_type" value="logged">

                                <!-- Add New Address -->
                                <div class="text-center mb-4">
                                    <div class="border p-3 bg-light rounded-3 cursor-pointer d-flex flex-column justify-content-center" onclick="add_new_address()">
                                        <i class="las la-plus la-2x mb-2"></i>
                                        <div class="text-muted fw-bold">{{ translate('Add New Address') }}</div>
                                    </div>
                                </div>

                                <div class="row align-items-center">
                                    <!-- Return to shop -->
                                    <div class="col-md-6 text-center text-md-start mb-2 mb-md-0">
                                        <a href="{{ route('home') }}" class="btn btn-link">
                                            <i class="las la-arrow-left"></i> {{ translate('Return to shop') }}
                                        </a>
                                    </div>
                                    <!-- Continue to Delivery Info -->
                                    @if(Auth::user()->addresses->count() > 0)
                                        <div class="col-md-6 text-center text-md-end">
                                            <button type="submit" class="primary__btn">{{ translate('Continue') }}</button>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif
                    </form>
                </div>
            </div>
        </div>
    </section>

@endsection

@section('modal')
    <!-- Address Modal -->
    @include('frontend.'.get_setting('homepage_select').'.partials.address_modal')
@endsection
