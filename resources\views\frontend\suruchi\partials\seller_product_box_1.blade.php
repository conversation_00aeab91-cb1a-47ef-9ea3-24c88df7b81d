@php
    $cart_added = [];

    $product_url = route('product', $product->slug);

    if ($product->auction_product == 1) {
        $product_url = route('auction-product', $product->slug);
    }

    $carts = get_user_cart();

    if (!empty($carts) && count($carts) > 0) {
        $cart_added = $carts->pluck('product_id')->toArray();
    }

    $photos = [];
@endphp
@if ($product->photos != null)
    @php
        $photos = explode(',', $product->photos);
    @endphp
@endif

<div class="col mb-30">
    <div class="product__items amazon-style-product">
        <div class="product__items--thumbnail amazon-product-image" style="height: 280px; position: relative; overflow: hidden; border-radius: 8px 8px 0 0;">
            <a class="product__items--link d-block h-100" href="{{ $product_url }}"
               onmouseenter="showProductImage({{ $product->id }})"
               onmouseleave="showProductThumnail({{ $product->id }})">
                <img class="product__items--img product__primary--img lazyload w-100 h-100 product-thumbnail-{{ $product->id }}"
                     style="height: 280px; object-fit: cover; transition: all 0.3s ease;"
                     src="{{ get_image($product->thumbnail) }}"
                     alt="{{ $product->getTranslation('name') }}"
                     title="{{ $product->getTranslation('name') }}"
                     onerror="this.onerror=null;this.src='{{ static_asset('assets/img/placeholder.jpg') }}';">

                @if(!empty($photos))
                    <img class="product__items--img product__primary--img lazyload w-100 h-100 product-image-{{ $product->id }}"
                         src="{{ uploaded_asset($photos[0]) }}"
                         alt="{{ $product->getTranslation('name') }}"
                         title="{{ $product->getTranslation('name') }}"
                         onerror="this.onerror=null;this.src='{{ static_asset('assets/img/placeholder.jpg') }}';"
                         style="display:none; height: 280px; object-fit: cover; position: absolute; top: 0; left: 0; transition: all 0.3s ease;">
                @endif
            </a>

            @if (discount_in_percentage($product) > 0)
                <div class="product__badge" style="position: absolute; top: 12px; left: 12px; z-index: 3;">
                    <span class="product__badge--items sale amazon-discount-badge">-{{ discount_in_percentage($product) }}%</span>
                </div>
            @endif

            <!-- Quick Action Buttons -->
            <div class="amazon-quick-actions">
                <button class="amazon-quick-btn" onclick="addToWishList({{ $product->id }})" title="{{ translate('Add to Wishlist') }}">
                    <i class="las la-heart"></i>
                </button>
                <button class="amazon-quick-btn" onclick="addToCompare({{ $product->id }})" title="{{ translate('Compare') }}">
                    <i class="las la-exchange-alt"></i>
                </button>
            </div>
        </div>

        <div class="product__items--content amazon-product-content" style="padding: 16px; background: #fff;">
            <!-- Category Badge -->
            @if($product->categories->first())
                <span class="amazon-category-badge">{{ $product->categories->first()->name }}</span>
            @endif

            <!-- Product Title -->
            <h3 class="product__items--content__title amazon-product-title">
                <a href="{{ $product_url }}" class="amazon-product-link">{{ Str::limit($product->getTranslation('name'), 50) }}</a>
            </h3>

            <!-- Rating Section -->
            <div class="amazon-rating-section d-flex align-items-center mb-2">
                <div class="rating product__rating d-flex amazon-stars">
                    {{ renderStarRating($product->rating) }}
                </div>
                <span class="amazon-rating-count">({{ $product->reviews->count() }})</span>
            </div>

            <!-- Price Section -->
            <div class="product__items--price amazon-price-section">
                @if ($product->auction_product == 0)
                    <div class="amazon-price-container">
                        @if (home_base_price($product) != home_discounted_base_price($product))
                            <span class="amazon-original-price">{{ home_base_price($product) }}</span>
                        @endif
                        <span class="amazon-current-price">{{ format_price($product->unit_price) }}</span>
                        @if (discount_in_percentage($product) > 0)
                            <span class="amazon-save-amount">{{ translate('Save') }} {{ discount_in_percentage($product) }}%</span>
                        @endif
                    </div>
                @endif
                @if ($product->auction_product == 1)
                    <div class="amazon-bid-section">
                        <span class="amazon-bid-label">{{ translate('Starting Bid') }}</span>
                        <span class="amazon-bid-price">{{ single_price($product->starting_bid) }}</span>
                    </div>
                @endif
            </div>

            <!-- Enhanced Action Buttons -->
            @if ($product->auction_product == 0)
                <div class="amazon-action-section mt-3">
                    <button class="amazon-add-to-cart-btn @if (in_array($product->id, $cart_added)) added @endif"
                            onclick="addToCartEvent({{ $product->id }})"
                            data-product-id="{{ $product->id }}">
                        <i class="las la-shopping-cart mr-2"></i>
                        <span class="amazon-cart-text">
                            @if (in_array($product->id, $cart_added))
                                {{ translate('Added to Cart') }}
                            @else
                                {{ translate('Add to Cart') }}
                            @endif
                        </span>
                    </button>
                </div>
            @else
                <div class="amazon-action-section mt-3">
                    <a href="{{ $product_url }}" class="amazon-bid-btn">
                        <i class="las la-gavel mr-2"></i>{{ translate('Place Bid') }}
                    </a>
                </div>
            @endif

            <!-- Delivery Info -->
            <div class="amazon-delivery-info mt-2">
                <small class="text-muted">
                    <i class="las la-shipping-fast mr-1"></i>{{ translate('Free delivery available') }}
                </small>
            </div>
        </div>
    </div>
</div>
