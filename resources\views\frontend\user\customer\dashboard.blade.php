@php
    $theme = get_setting('homepage_select');
@endphp

@extends('frontend.'.$theme.'.layouts.user_panel')

@section('panel_content')

    @php
        $welcomeCoupon = ifUserHasWelcomeCouponAndNotUsed();
    @endphp
    @if($welcomeCoupon)
        <div class="alert alert-primary align-items-center border d-flex flex-wrap justify-content-between" style="border-color: #3490F3 !important;">
            @php
                $discount = $welcomeCoupon->discount_type == 'amount' ? single_price($welcomeCoupon->discount) : $welcomeCoupon->discount.'%';
            @endphp
            <div class="fw-400 fs-14" style="color: #3490F3 !important;">
                {{ translate('Welcome Coupon') }} <strong>{{ $discount }}</strong> {{ translate('Discount on your Purchase Within') }} <strong>{{ $welcomeCoupon->validation_days }}</strong> {{ translate('days of Registration') }}
            </div>
            <button class="btn btn-sm mt-3 mt-lg-0 rounded-4" onclick="copyCouponCode('{{ $welcomeCoupon->coupon_code }}')" style="background-color: #3490F3; color: white;" >{{ translate('Copy coupon Code') }}</button>
        </div>
    @endif

    <div class="row gutters-16">
        <!-- Wallet summary -->
        @if (get_setting('wallet_system') == 1)
        <div class="col-xl-8 col-md-6 mb-4">
            <div class="h-100" style="background-image: url('{{ static_asset("assets/img/wallet-bg.png") }}'); background-size: cover; background-position: center center;">
                <div class="p-4 h-100 w-100 w-xl-50">
                    <p class="fs-14 fw-400 text-gray mb-3">{{ translate('Wallet Balance') }}</p>
                    <h1 class="fs-30 fw-700 text-white ">{{ single_price(Auth::user()->balance) }}</h1>
                    <hr class="border border-dashed border-white opacity-40 ml-0 mt-4 mb-4">
                    @php
                        $last_recharge = get_user_last_wallet_recharge();
                    @endphp
                    <p class="fs-14 fw-400 text-gray mb-1">{{ translate('Last Recharge') }} <strong>{{ $last_recharge ? date('d.m.Y', strtotime($last_recharge->created_at)) : '' }}</strong></p>
                    <h3 class="fs-20 fw-700 text-white ">{{ $last_recharge ? single_price($last_recharge->amount) : 0 }}</h3>
                    <button class="btn btn-block border border-soft-light hov-bg-dark text-white mt-5 py-3" onclick="show_wallet_modal()" style="border-radius: 30px; background: rgba(255, 255, 255, 0.1);">
                        <i class="la la-plus fs-18 fw-700 mr-2"></i>
                        {{ translate('Recharge Wallet') }}
                    </button>
                </div>
            </div>
        </div>
        @endif

        <div class="col mb-4">
            <div class="h-100">
                <div class="row h-100 @if(get_setting('wallet_system') != 1 && addon_is_activated('club_point')) row-cols-md-2 @endif row-cols-1">
                    <!-- Expenditure summary -->
                    <div class="col">
                        <div class="p-4 bg-primary @if(!addon_is_activated('club_point')) h-100 @endif" style="margin-bottom: 2rem;">
                            <div class="d-flex align-items-center pb-4 ">
                                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48">
                                    <g id="Group_25000" data-name="Group 25000" transform="translate(-926 -614)">
                                    <rect id="Rectangle_18646" data-name="Rectangle 18646" width="48" height="48" rx="24" transform="translate(926 614)" fill="rgba(255,255,255,0.5)"/>
                                    <g id="Group_24786" data-name="Group 24786" transform="translate(701.466 93)">
                                        <path id="Path_32311" data-name="Path 32311" d="M122.052,10V8.55a.727.727,0,1,0-1.455,0V10a2.909,2.909,0,0,0-2.909,2.909v.727A2.909,2.909,0,0,0,120.6,16.55h1.455A1.454,1.454,0,0,1,123.506,18v.727a1.454,1.454,0,0,1-1.455,1.455H120.6a1.454,1.454,0,0,1-1.455-1.455.727.727,0,1,0-1.455,0,2.909,2.909,0,0,0,2.909,2.909V23.1a.727.727,0,1,0,1.455,0V21.641a2.909,2.909,0,0,0,2.909-2.909V18a2.909,2.909,0,0,0-2.909-2.909H120.6a1.454,1.454,0,0,1-1.455-1.455v-.727a1.454,1.454,0,0,1,1.455-1.455h1.455a1.454,1.454,0,0,1,1.455,1.455.727.727,0,0,0,1.455,0A2.909,2.909,0,0,0,122.052,10" transform="translate(127.209 529.177)" fill="#fff"/>
                                    </g>
                                    </g>
                                </svg>
                                <div class="ml-3 d-flex flex-column justify-content-between">
                                    <span class="fs-14 fw-400 text-white mb-1">{{ translate('Total Expenditure') }}</span>
                                    <span class="fs-20 fw-700 text-white">{{ single_price(get_user_total_expenditure()) }}</span>
                                </div>
                            </div>
                            <a href="{{ route('purchase_history.index') }}" class="fs-12 text-white">
                                {{ translate('View Order History') }}
                                <i class="las la-angle-right fs-14"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Club Point summary -->
                    @if (addon_is_activated('club_point'))
                    <div class="col">
                        <div class="p-4 bg-secondary-base">
                            <div class="d-flex align-items-center pb-4 ">
                                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48">
                                    <g id="Group_25000" data-name="Group 25000" transform="translate(-926 -614)">
                                    <rect id="Rectangle_18646" data-name="Rectangle 18646" width="48" height="48" rx="24" transform="translate(926 614)" fill="rgba(255,255,255,0.5)"/>
                                    <g id="Group_24786" data-name="Group 24786" transform="translate(701.466 93)">
                                        <path id="Path_2961" data-name="Path 2961" d="M221.069,0a8,8,0,1,0,8,8,8,8,0,0,0-8-8m0,15a7,7,0,1,1,7-7,7,7,0,0,1-7,7" transform="translate(27.466 537)" fill="#fff"/>
                                        <path id="Union_11" data-name="Union 11" d="M16425.393,420.226l-3.777-5.039a.42.42,0,0,1-.012-.482l1.662-2.515a.416.416,0,0,1,.313-.186l0,0h4.26a.41.41,0,0,1,.346.19l1.674,2.515a.414.414,0,0,1-.012.482l-3.777,5.039a.413.413,0,0,1-.338.169A.419.419,0,0,1,16425.393,420.226Zm-2.775-5.245,3.113,4.148,3.109-4.148-1.32-1.983h-3.592Z" transform="translate(-16177.195 129)" fill="#fff"/>
                                    </g>
                                    </g>
                                </svg>
                                <div class="ml-3 d-flex flex-column justify-content-between">
                                    <span class="fs-14 fw-400 text-white mb-1">{{ translate('Total Club Points') }}</span>
                                    <span class="fs-20 fw-700 text-white">{{ get_user_total_club_point() }}</span>
                                </div>
                            </div>
                            <a href="{{ route('earnng_point_for_user') }}" class="fs-12 text-white">
                                {{ translate('Convert Club Points') }}
                                <i class="las la-angle-right fs-14"></i>
                            </a>
                        </div>
                    </div>
                    @endif

                </div>
            </div>
        </div>
    </div>

    <div class="row gutters-16 mt-2">

        <!-- count summary - Horizontal Layout -->
        <div class="col-12 mb-4">
            <div class="row gutters-16">
                <!-- Cart summary -->
                <div class="col-lg-4 col-md-6 mb-3 mb-lg-0">
                    <div class="dashboard-summary-card bg-white border rounded-lg shadow-sm h-100 overflow-hidden position-relative">
                        <div class="p-4 d-flex align-items-center">
                            <div class="flex-shrink-0 mr-4">
                                <div class="icon-circle rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; background: linear-gradient(135deg, #d43533 0%, #ff6b6b 100%);">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="9" cy="21" r="1"></circle>
                                        <circle cx="20" cy="21" r="1"></circle>
                                        <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                @php
                                    $cart = get_user_cart();
                                @endphp
                                <div class="fs-28 fw-700 text-dark mb-1">{{ count($cart) > 0 ? sprintf("%02d", count($cart)) : '00' }}</div>
                                <div class="fs-14 fw-500 text-muted">{{ translate('Products in Cart') }}</div>
                                <a href="{{ route('cart') }}" class="fs-12 text-primary fw-600 mt-2 d-inline-block">
                                    {{ translate('View Cart') }} <i class="las la-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                        <div class="position-absolute" style="top: -10px; right: -10px; width: 40px; height: 40px; background: rgba(212, 53, 51, 0.1); border-radius: 50%;"></div>
                    </div>
                </div>

                <!-- Wishlist summary -->
                <div class="col-lg-4 col-md-6 mb-3 mb-lg-0">
                    <div class="bg-white border rounded-lg shadow-sm h-100 overflow-hidden position-relative">
                        <div class="p-4 d-flex align-items-center">
                            <div class="flex-shrink-0 mr-4">
                                <div class="rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; background: linear-gradient(135deg, #3490f3 0%, #74b9ff 100%);">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fs-28 fw-700 text-dark mb-1">{{ count(Auth::user()->wishlists) > 0 ? sprintf("%02d", count(Auth::user()->wishlists)) : '00' }}</div>
                                <div class="fs-14 fw-500 text-muted">{{ translate('Products in Wishlist') }}</div>
                                <a href="{{ route('wishlists.index') }}" class="fs-12 text-primary fw-600 mt-2 d-inline-block">
                                    {{ translate('View Wishlist') }} <i class="las la-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                        <div class="position-absolute" style="top: -10px; right: -10px; width: 40px; height: 40px; background: rgba(52, 144, 243, 0.1); border-radius: 50%;"></div>
                    </div>
                </div>

                <!-- Order summary -->
                <div class="col-lg-4 col-md-6 mb-3 mb-lg-0">
                    <div class="bg-white border rounded-lg shadow-sm h-100 overflow-hidden position-relative">
                        <div class="p-4 d-flex align-items-center">
                            <div class="flex-shrink-0 mr-4">
                                <div class="rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; background: linear-gradient(135deg, #85b567 0%, #a8d8a8 100%);">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"></path>
                                        <path d="m3.3 7 8.7 5 8.7-5"></path>
                                        <path d="M12 22V12"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                @php
                                   $total =  get_user_total_ordered_products();
                                @endphp
                                <div class="fs-28 fw-700 text-dark mb-1">{{ $total > 0 ? sprintf("%02d", $total) : '00' }}</div>
                                <div class="fs-14 fw-500 text-muted">{{ translate('Total Products Ordered') }}</div>
                                <a href="{{ route('purchase_history.index') }}" class="fs-12 text-primary fw-600 mt-2 d-inline-block">
                                    {{ translate('View Orders') }} <i class="las la-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                        <div class="position-absolute" style="top: -10px; right: -10px; width: 40px; height: 40px; background: rgba(133, 181, 103, 0.1); border-radius: 50%;"></div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="row align-items-center mb-2 mt-1">
        <div class="col-6">
            <h3 class=" mb-0 fs-14 fs-md-16 fw-700 text-dark">{{ translate('My Wishlist')}}</h3>
        </div>
        <div class="col-6 text-right">
            <a class="text-blue fs-10 fs-md-12 fw-700 hov-text-primary animate-underline-primary" href="{{ route('wishlists.index') }}">{{ translate('View All') }}</a>
        </div>
    </div>
    @php
        $wishlists = get_user_wishlist();
    @endphp
    @if (count($wishlists) > 0)
        <div class="row row-cols-xxl-5 row-cols-xl-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-2 gutters-16 border-top border-left mx-1 mx-md-0 mb-4">
            @foreach($wishlists->take(5) as $key => $wishlist)
                @if ($wishlist->product != null)
                    <div class="aiz-card-box col py-3 text-center border-right border-bottom has-transition hov-shadow-out z-1" id="wishlist_{{ $wishlist->id }}">
                        <div class="position-relative h-140px h-md-200px img-fit overflow-hidden mb-3">
                            <!-- Image -->
                            <a href="{{ route('product', $wishlist->product->slug) }}" class="d-block h-100">
                                <img src="{{ uploaded_asset($wishlist->product->thumbnail_img) }}" class="lazyload mx-auto img-fit"
                                    title="{{ $wishlist->product->getTranslation('name') }}">
                            </a>
                            <!-- Remove from wishlisht -->
                            <div class="absolute-top-right aiz-p-hov-icon">
                                <a href="javascript:void(0)" onclick="removeFromWishlist({{ $wishlist->id }})" data-toggle="tooltip" data-title="{{ translate('Remove from wishlist') }}" data-placement="left">
                                    <i class="la la-trash"></i>
                                </a>
                            </div>
                            <!-- add to cart -->
                            <a class="cart-btn absolute-bottom-left w-100 h-35px aiz-p-hov-icon text-white fs-13 fw-700 d-flex justify-content-center align-items-center"
                                href="javascript:void(0)" onclick="showAddToCartModal({{ $wishlist->product->id }})">{{ translate('Add to Cart') }}</a>
                        </div>
                        <!-- Product Name -->
                        <h5 class="fs-14 mb-0 lh-1-5 fw-400 text-truncate-2 mb-3">
                            <a href="{{ route('product', $wishlist->product->slug) }}" class="text-reset hov-text-primary"
                                title="{{ $wishlist->product->getTranslation('name') }}">{{ $wishlist->product->getTranslation('name') }}</a>
                        </h5>
                        <!-- Price -->
                        <div class="fs-14">
                            <span class="fw-600 text-primary">{{ home_discounted_base_price($wishlist->product) }}</span>
                            @if(home_base_price($wishlist->product) != home_discounted_base_price($wishlist->product))
                                <del class="opacity-60 ml-1">{{ home_base_price($wishlist->product) }}</del>
                            @endif
                        </div>
                    </div>
                @endif
            @endforeach
        </div>
    @else
        <div class="row">
            <div class="col">
                <div class="text-center bg-white p-4 border">
                    <img class="mw-100 h-200px" src="{{ static_asset('assets/img/nothing.svg') }}" alt="Image">
                    <h5 class="mb-0 h5 mt-3">{{ translate("There isn't anything added yet")}}</h5>
                </div>
            </div>
        </div>
    @endif
@endsection

@section('modal')
    <!-- Wallet Recharge Modal -->
    @include('frontend.'.get_setting('homepage_select').'.partials.wallet_modal')
    <script type="text/javascript">
        function show_wallet_modal() {
            $('#wallet_modal').modal('show').addClass('is-visible');
        }
    </script>

    <!-- Address modal Modal -->
    @include('frontend.'.get_setting('homepage_select').'.partials.address_modal')
@endsection

@section('script')
    @if (get_setting('google_map') == 1)
        @include('frontend.'.get_setting('homepage_select').'.partials.google_map')
    @endif
@endsection
