@php
    $theme = get_setting('homepage_select');
@endphp

@extends('frontend.'.$theme.'.layouts.user_panel')

@section('meta')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/css/intlTelInput.css">
@endsection

@section('panel_content')
    <div class="aiz-titlebar mb-4">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="fs-20 fw-700 text-dark">{{ translate('Manage Profile') }}</h1>
            </div>
        </div>
    </div>

    <!-- Basic Info-->
    <div class="card rounded-0 shadow-none border">
        <div class="card-header pt-4 border-bottom-0">
            <h5 class="mb-0 fs-18 fw-700 text-dark">{{ translate('Basic Info')}}</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('user.profile.update') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <!-- Name-->
                <div class="form-group row">
                    <label class="col-md-2 col-form-label fs-14 fs-14">{{ translate('Your Name') }}</label>
                    <div class="col-md-10">
                        <input type="text" class="checkout__input--field" placeholder="{{ translate('Your Name') }}" name="name" value="{{ Auth::user()->name }}">
                    </div>
                </div>
                <!-- Phone-->
                <div class="form-group row">
                    <label class="col-md-2 col-form-label fs-14">{{ translate('Your Phone') }}</label>
                    <div class="col-md-10">
                        <input type="tel" id="phone" class="checkout__input--field" placeholder="{{ translate('Your Phone')}}" name="phone" value="{{ Auth::user()->phone }}">
                        <input type="hidden" name="country_code" id="country_code" value="">
                    </div>
                </div>
                <!-- Photo-->
                <div class="form-group row">
                    <label class="col-md-2 col-form-label fs-14">{{ translate('Photo') }}</label>
                    <div class="col-md-10">
                        <div class="input-group py-2" data-toggle="aizuploader" data-type="image">
                            <div class="input-group-prepend py-2">
                                <div class="input-group-text bg-soft-secondary font-weight-medium rounded-0 py-3">{{ translate('Browse')}}</div>
                            </div>
                            <div class="form-control file-amount py-2">{{ translate('Choose File') }}</div>
                            <input type="hidden" name="photo" value="{{ Auth::user()->avatar_original }}" class="checkout__input--field selected-files">
                        </div>
                        <div class="file-preview box sm">
                        </div>
                    </div>
                </div>
                <!-- Password-->
                <div class="form-group row">
                    <label class="col-md-2 col-form-label fs-14">{{ translate('Your Password') }}</label>
                    <div class="col-md-10">
                        <input type="password" class="checkout__input--field" placeholder="{{ translate('New Password') }}" name="new_password">
                    </div>
                </div>
                <!-- Confirm Password-->
                <div class="form-group row">
                    <label class="col-md-2 col-form-label fs-14">{{ translate('Confirm Password') }}</label>
                    <div class="col-md-10">
                        <input type="password" class="checkout__input--field" placeholder="{{ translate('Confirm Password') }}" name="confirm_password">
                    </div>
                </div>
                <!-- Submit Button-->
                <div class="form-group mb-0 text-right">
                    <button type="submit" class="continue__shipping--btn primary__btn border-radius-5">{{translate('Update Profile')}}</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Address -->
    <div class="card rounded-0 shadow-none border">
        <div class="card-header pt-4 border-bottom-0">
            <h5 class="mb-0 fs-18 fw-700 text-dark">{{ translate('Address')}}</h5>
        </div>
        <div class="card-body">
            @foreach (Auth::user()->addresses as $key => $address)
                <div class="">
                    <div class="border p-4 mb-4 position-relative">
                        <div class="row fs-14 mb-2 mb-md-0">
                            <span class="col-md-2 text-secondary">{{ translate('Address') }}:</span>
                            <span class="col-md-8 text-dark">{{ $address->address }}</span>
                        </div>
                        <div class="row fs-14 mb-2 mb-md-0">
                            <span class="col-md-2 text-secondary">{{ translate('Postal Code') }}:</span>
                            <span class="col-md-10 text-dark">{{ $address->postal_code }}</span>
                        </div>
                        <div class="row fs-14 mb-2 mb-md-0">
                            <span class="col-md-2 text-secondary">{{ translate('City') }}:</span>
                            <span class="col-md-10 text-dark">{{ optional($address->city)->name }}</span>
                        </div>
                        <div class="row fs-14 mb-2 mb-md-0">
                            <span class="col-md-2 text-secondary">{{ translate('State') }}:</span>
                            <span class="col-md-10 text-dark">{{ optional($address->state)->name }}</span>
                        </div>
                        <div class="row fs-14 mb-2 mb-md-0">
                            <span class="col-md-2 text-secondary">{{ translate('Country') }}:</span>
                            <span class="col-md-10 text-dark">{{ optional($address->country)->name }}</span>
                        </div>
                        <div class="row fs-14 mb-2 mb-md-0">
                            <span class="col-md-2 text-secondary text-secondary">{{ translate('Phone') }}:</span>
                            <span class="col-md-10 text-dark">{{ $address->phone }}</span>
                        </div>
                        @if ($address->set_default)
                            <div class="absolute-md-top-right pt-2 pt-md-4 pr-md-5">
                                <span class="badge badge-inline badge-secondary-base text-white p-3 fs-12" style="border-radius: 25px; min-width: 80px !important;">{{ translate('Default') }}</span>
                            </div>
                        @endif
                        <div class="dropdown position-absolute right-0 top-0 pt-4 mr-1">
                            <button class="btn bg-gray text-white px-1 py-1" type="button" data-toggle="dropdown">
                                <i class="la la-ellipsis-v"></i>
                            </button>
                            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                                <a class="dropdown-item" onclick="edit_address('{{$address->id}}')">
                                    <i class="las la-edit"></i> {{ translate('Edit') }}
                                </a>
                                @if (!$address->set_default)
                                    <a class="dropdown-item" href="{{ route('addresses.set_default', $address->id) }}">
                                        <i class="las la-check-circle"></i> {{ translate('Make This Default') }}
                                    </a>
                                @endif
                                @if (!$address->set_default)
                                    <a class="dropdown-item text-danger" onclick="confirm_delete_address('{{$address->id}}')">
                                        <i class="las la-trash"></i> {{ translate('Delete') }}
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
            <!-- Add New Address -->
            <div class="" onclick="add_new_address()">
                <div class="border p-3 mb-3 c-pointer text-center bg-light has-transition hov-bg-soft-light">
                    <i class="la la-plus la-2x"></i>
                    <div class="alpha-7 fs-14 fw-700">{{ translate('Add New Address') }}</div>
                </div>
            </div>
        </div>
    </div>


    <!-- Email Verification -->
    <div class="card rounded-0 shadow-none border">
        <div class="card-header pt-4 border-bottom-0">
            <h5 class="mb-0 fs-18 fw-700 text-dark">
                {{ translate('Email Verification')}}
                @if(Auth::user()->hasVerifiedEmail())
                    <span class="badge badge-success ml-2">{{ translate('Verified') }}</span>
                @else
                    <span class="badge badge-warning ml-2">{{ translate('Not Verified') }}</span>
                @endif
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <label class="fs-14">{{ translate('Your Email') }}</label>
                </div>
                <div class="col-md-10">
                    <div class="input-group mb-3">
                        <input type="email" id="email-input" class="checkout__input--field" placeholder="{{ translate('Your Email')}}" value="{{ Auth::user()->email }}" />
                        <div class="input-group-append">
                            <button type="button" class="btn btn-outline-secondary" id="send-email-otp">
                                <span class="d-none loading">
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>{{ translate('Sending...') }}
                                </span>
                                <span class="default">{{ translate('Send OTP') }}</span>
                            </button>
                        </div>
                    </div>

                    <!-- OTP Input (hidden initially) -->
                    <div class="input-group mb-3 d-none" id="email-otp-section">
                        <input type="text" id="email-otp-input" class="checkout__input--field" placeholder="{{ translate('Enter 6-digit OTP')}}" maxlength="6" />
                        <div class="input-group-append">
                            <button type="button" class="btn btn-success" id="verify-email-otp">
                                <span class="d-none loading">
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>{{ translate('Verifying...') }}
                                </span>
                                <span class="default">{{ translate('Verify') }}</span>
                            </button>
                        </div>
                    </div>

                    <div class="text-muted fs-12" id="email-status">
                        @if(Auth::user()->hasVerifiedEmail())
                            <i class="las la-check-circle text-success"></i> {{ translate('Email verified successfully') }}
                        @else
                            <i class="las la-exclamation-triangle text-warning"></i> {{ translate('Please verify your email address') }}
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Phone Verification -->
    <div class="card rounded-0 shadow-none border">
        <div class="card-header pt-4 border-bottom-0">
            <h5 class="mb-0 fs-18 fw-700 text-dark">
                {{ translate('Phone Verification')}}
                @if(Auth::user()->hasVerifiedPhone())
                    <span class="badge badge-success ml-2">{{ translate('Verified') }}</span>
                @else
                    <span class="badge badge-warning ml-2">{{ translate('Not Verified') }}</span>
                @endif
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <label class="fs-14">{{ translate('Your Phone') }}</label>
                </div>
                <div class="col-md-10">
                    <div class="input-group mb-3">
                        <input type="tel" id="phone-verification" class="checkout__input--field" placeholder="{{ translate('Your Phone')}}" value="{{ Auth::user()->phone_without_country_code }}" />
                        <input type="hidden" id="phone-country-code" value="{{ Auth::user()->country_code }}">
                        <div class="input-group-append">
                            <button type="button" class="btn btn-outline-secondary" id="send-phone-otp">
                                <span class="d-none loading">
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>{{ translate('Sending...') }}
                                </span>
                                <span class="default">{{ translate('Send OTP') }}</span>
                            </button>
                        </div>
                    </div>

                    <!-- OTP Input (hidden initially) -->
                    <div class="input-group mb-3 d-none" id="phone-otp-section">
                        <input type="text" id="phone-otp-input" class="checkout__input--field" placeholder="{{ translate('Enter 6-digit OTP')}}" maxlength="6" />
                        <div class="input-group-append">
                            <button type="button" class="btn btn-success" id="verify-phone-otp">
                                <span class="d-none loading">
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>{{ translate('Verifying...') }}
                                </span>
                                <span class="default">{{ translate('Verify') }}</span>
                            </button>
                        </div>
                    </div>

                    <div class="text-muted fs-12" id="phone-status">
                        @if(Auth::user()->hasVerifiedPhone())
                            <i class="las la-check-circle text-success"></i> {{ translate('Phone verified successfully') }}
                        @else
                            <i class="las la-exclamation-triangle text-warning"></i> {{ translate('Please verify your phone number') }}
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

        <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/intlTelInput.min.js"></script>
    <script type="text/javascript">
        // Initialize intl-tel-input for profile phone
        var phoneInput = document.querySelector("#phone");
        var iti = window.intlTelInput(phoneInput, {
            initialCountry: "auto",
            geoIpLookup: function(callback) {
                fetch('https://ipapi.co/json')
                    .then(function(res) { return res.json(); })
                    .then(function(data) { callback(data.country_code); })
                    .catch(function() { callback("us"); });
            },
            utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/utils.js",
            separateDialCode: true,
            nationalMode: false,
            formatOnDisplay: true,
            autoPlaceholder: "aggressive"
        });

        // Update country code when country changes
        phoneInput.addEventListener('countrychange', function() {
            var countryCode = iti.getSelectedCountryData().dialCode;
            document.getElementById('country_code').value = '+' + countryCode;
        });

        // Set country code on page load
        phoneInput.addEventListener('ready', function() {
            var countryCode = iti.getSelectedCountryData().dialCode;
            document.getElementById('country_code').value = '+' + countryCode;
        });

        // Form submission - ensure we capture the full international number and country code
        $('form').on('submit', function() {
            var countryCode = '+' + iti.getSelectedCountryData().dialCode;
            var phoneNumber = iti.getNumber(intlTelInputUtils.numberFormat.E164);

            document.getElementById('country_code').value = countryCode;
            if (phoneNumber) {
                $('#phone').val(phoneNumber);
            }
        });

        // Initialize intl-tel-input for phone verification
        var phoneVerificationInput = document.querySelector("#phone-verification");
        var itiVerification = window.intlTelInput(phoneVerificationInput, {
            initialCountry: "auto",
            geoIpLookup: function(callback) {
                fetch('https://ipapi.co/json')
                    .then(function(res) { return res.json(); })
                    .then(function(data) { callback(data.country_code); })
                    .catch(function() { callback("us"); });
            },
            utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/utils.js",
            separateDialCode: true,
            nationalMode: false,
            formatOnDisplay: true,
            autoPlaceholder: "aggressive"
        });

        // Update country code when country changes for verification
        phoneVerificationInput.addEventListener('countrychange', function() {
            var countryCode = itiVerification.getSelectedCountryData().dialCode;
            document.getElementById('phone-country-code').value = '+' + countryCode;
        });

        // Set initial country code for verification
        phoneVerificationInput.addEventListener('ready', function() {
            var countryCode = itiVerification.getSelectedCountryData().dialCode;
            document.getElementById('phone-country-code').value = '+' + countryCode;
        });



        // Email OTP functionality
        $('#send-email-otp').on('click', function() {
            var button = $(this);
            var email = $('#email-input').val();

            if (!email) {
                showNotification('error', '{{ translate("Please enter an email address") }}');
                return;
            }

            button.find('.loading').removeClass('d-none');
            button.find('.default').addClass('d-none');
            button.prop('disabled', true);

            $.post('{{ route('user.verification.send-email-otp') }}', {
                _token: '{{ csrf_token() }}',
                email: email
            }, function(response) {
                if (response.success) {
                    $('#email-otp-section').removeClass('d-none');
                    showNotification('success', response.message);
                    startCountdown(button, 120); // 2 minutes countdown
                } else {
                    showNotification('error', response.message);
                    button.find('.loading').addClass('d-none');
                    button.find('.default').removeClass('d-none');
                    button.prop('disabled', false);
                }
            }).fail(function() {
                showNotification('error', '{{ translate("Something went wrong. Please try again.") }}');
                button.find('.loading').addClass('d-none');
                button.find('.default').removeClass('d-none');
                button.prop('disabled', false);
            });
        });

        // Phone OTP functionality
        $('#send-phone-otp').on('click', function() {
            var button = $(this);
            var phone = itiVerification.getNumber();
            var countryCode = '+' + itiVerification.getSelectedCountryData().dialCode;

            if (!phone) {
                showNotification('error', '{{ translate("Please enter a phone number") }}');
                return;
            }

            button.find('.loading').removeClass('d-none');
            button.find('.default').addClass('d-none');
            button.prop('disabled', true);

            $.post('{{ route('user.verification.send-phone-otp') }}', {
                _token: '{{ csrf_token() }}',
                phone: phone.replace(countryCode, ''),
                country_code: countryCode
            }, function(response) {
                if (response.success) {
                    $('#phone-otp-section').removeClass('d-none');
                    showNotification('success', response.message);
                    startCountdown(button, 120); // 2 minutes countdown
                } else {
                    showNotification('error', response.message);
                    button.find('.loading').addClass('d-none');
                    button.find('.default').removeClass('d-none');
                    button.prop('disabled', false);
                }
            }).fail(function() {
                showNotification('error', '{{ translate("Something went wrong. Please try again.") }}');
                button.find('.loading').addClass('d-none');
                button.find('.default').removeClass('d-none');
                button.prop('disabled', false);
            });
        });

        // Verify Email OTP
        $('#verify-email-otp').on('click', function() {
            var button = $(this);
            var otp = $('#email-otp-input').val();

            if (!otp || otp.length !== 6) {
                showNotification('error', '{{ translate("Please enter a valid 6-digit OTP") }}');
                return;
            }

            button.find('.loading').removeClass('d-none');
            button.find('.default').addClass('d-none');
            button.prop('disabled', true);

            $.post('{{ route('user.verification.verify-email-otp') }}', {
                _token: '{{ csrf_token() }}',
                otp: otp
            }, function(response) {
                if (response.success) {
                    $('#email-otp-section').addClass('d-none');
                    $('#email-status').html('<i class="las la-check-circle text-success"></i> {{ translate("Email verified successfully") }}');
                    $('.card-header h5').find('.badge').removeClass('badge-warning').addClass('badge-success').text('{{ translate("Verified") }}');
                    showNotification('success', response.message);
                    location.reload(); // Refresh to update verification status
                } else {
                    showNotification('error', response.message);
                }
                button.find('.loading').addClass('d-none');
                button.find('.default').removeClass('d-none');
                button.prop('disabled', false);
            }).fail(function() {
                showNotification('error', '{{ translate("Something went wrong. Please try again.") }}');
                button.find('.loading').addClass('d-none');
                button.find('.default').removeClass('d-none');
                button.prop('disabled', false);
            });
        });

        // Verify Phone OTP
        $('#verify-phone-otp').on('click', function() {
            var button = $(this);
            var otp = $('#phone-otp-input').val();

            if (!otp || otp.length !== 6) {
                showNotification('error', '{{ translate("Please enter a valid 6-digit OTP") }}');
                return;
            }

            button.find('.loading').removeClass('d-none');
            button.find('.default').addClass('d-none');
            button.prop('disabled', true);

            $.post('{{ route('user.verification.verify-phone-otp') }}', {
                _token: '{{ csrf_token() }}',
                otp: otp
            }, function(response) {
                if (response.success) {
                    $('#phone-otp-section').addClass('d-none');
                    $('#phone-status').html('<i class="las la-check-circle text-success"></i> {{ translate("Phone verified successfully") }}');
                    $('.card-header h5').find('.badge').removeClass('badge-warning').addClass('badge-success').text('{{ translate("Verified") }}');
                    showNotification('success', response.message);
                    location.reload(); // Refresh to update verification status
                } else {
                    showNotification('error', response.message);
                }
                button.find('.loading').addClass('d-none');
                button.find('.default').removeClass('d-none');
                button.prop('disabled', false);
            }).fail(function() {
                showNotification('error', '{{ translate("Something went wrong. Please try again.") }}');
                button.find('.loading').addClass('d-none');
                button.find('.default').removeClass('d-none');
                button.prop('disabled', false);
            });
        });

        // Helper functions
        function showNotification(type, message) {
            if (typeof AIZ !== 'undefined' && AIZ.plugins && AIZ.plugins.notify) {
                AIZ.plugins.notify(type, message);
            } else {
                alert(message);
            }
        }

        function startCountdown(button, seconds) {
            var originalText = button.find('.default').text();
            var countdown = seconds;

            var timer = setInterval(function() {
                button.find('.default').text('{{ translate("Resend in") }} ' + countdown + 's');
                countdown--;

                if (countdown < 0) {
                    clearInterval(timer);
                    button.find('.default').text(originalText);
                    button.find('.loading').addClass('d-none');
                    button.find('.default').removeClass('d-none');
                    button.prop('disabled', false);
                }
            }, 1000);
        }

        // Address management functions
        function confirm_delete_address(addressId) {
            if (confirm('{{ translate("Are you sure you want to delete this address?") }}')) {
                window.location.href = '{{ url("addresses/destroy") }}/' + addressId;
            }
        }

        // Show success/error messages
        function showNotification(type, message) {
            if (type === 'success') {
                AIZ.plugins.notify('success', message);
            } else {
                AIZ.plugins.notify('danger', message);
            }
        }
    </script>
@endsection

@section('modal')
    <!-- Address modal -->
    @include('frontend.'.get_setting('homepage_select').'.partials.address_modal')
@endsection

@if (get_setting('google_map') == 1)
    @include('frontend.'.get_setting('homepage_select').'.partials.google_map')
@endif

