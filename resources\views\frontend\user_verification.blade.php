@extends('frontend.suruchi.layouts.app')

@section('content')
    <style>
        .verification-wrapper {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .verification-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 1rem;
            position: relative;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .step.completed {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        
        .step.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            animation: pulse 2s infinite;
        }
        
        .step.pending {
            background: #e9ecef;
            color: #6c757d;
        }
        
        .step-connector {
            width: 80px;
            height: 3px;
            background: #e9ecef;
            margin-top: 28px;
        }
        
        .step-connector.completed {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .verification-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .verification-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }
        
        .verification-section.completed {
            border-left: 5px solid #28a745;
        }
        
        .verification-section.pending {
            border-left: 5px solid #ffc107;
        }
        
        .verify-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .verify-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .verify-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .status-verified {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .continue-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            color: white;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
        }
        
        .continue-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(40, 167, 69, 0.4);
            color: white;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .verification-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .verification-icon.email {
            color: #667eea;
        }
        
        .verification-icon.phone {
            color: #764ba2;
        }
        
        .verification-title {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .user-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .logout-link {
            color: #dc3545;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .logout-link:hover {
            color: #c82333;
            text-decoration: underline;
        }
    </style>

    <div class="verification-wrapper d-flex align-items-center justify-content-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-md-10">
                    <div class="verification-card p-4 p-md-5">
                        <!-- Header -->
                        <div class="text-center mb-4">
                            <img src="{{ uploaded_asset(get_setting('site_icon')) }}" 
                                 alt="{{ translate('Site Icon') }}" 
                                 style="width: 60px; height: 60px; margin-bottom: 1rem;">
                            <h1 class="verification-title">{{ translate('Complete Your Verification') }}</h1>
                            <p class="text-muted">{{ translate('Please verify both your email and phone number to continue') }}</p>
                        </div>

                        <!-- User Info -->
                        <div class="user-info">
                            <h5 class="mb-2">{{ translate('Welcome') }}, {{ auth()->user()->name }}!</h5>
                            <p class="mb-0 text-muted">
                                @if(auth()->user()->user_type == 'seller')
                                    {{ translate('As a seller, you need to verify your contact information before accessing your dashboard.') }}
                                @else
                                    {{ translate('Please complete the verification process to access all features.') }}
                                @endif
                            </p>
                        </div>

                        <!-- Step Indicator -->
                        <div class="step-indicator">
                            <div class="step {{ auth()->user()->hasVerifiedEmail() ? 'completed' : 'active' }}">
                                <i class="las la-envelope"></i>
                            </div>
                            <div class="step-connector {{ auth()->user()->hasVerifiedEmail() ? 'completed' : '' }}"></div>
                            <div class="step {{ auth()->user()->hasVerifiedPhone() ? 'completed' : (auth()->user()->hasVerifiedEmail() ? 'active' : 'pending') }}">
                                <i class="las la-phone"></i>
                            </div>
                        </div>

                        <!-- Email Verification Section -->
                        <div class="verification-section {{ auth()->user()->hasVerifiedEmail() ? 'completed' : 'pending' }}">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex align-items-center">
                                        <i class="las la-envelope verification-icon email"></i>
                                        <div class="ms-3">
                                            <h5 class="mb-1">{{ translate('Email Verification') }}</h5>
                                            <p class="mb-1 text-muted">{{ auth()->user()->email }}</p>
                                            <span class="status-badge {{ auth()->user()->hasVerifiedEmail() ? 'status-verified' : 'status-pending' }}">
                                                @if(auth()->user()->hasVerifiedEmail())
                                                    <i class="las la-check-circle me-1"></i>{{ translate('Verified') }}
                                                @else
                                                    <i class="las la-clock me-1"></i>{{ translate('Pending') }}
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    @if(!auth()->user()->hasVerifiedEmail())
                                        <a href="{{ route('verification.notice') }}" class="btn verify-btn">
                                            {{ translate('Verify Email') }}
                                        </a>
                                    @else
                                        <i class="las la-check-circle text-success" style="font-size: 2rem;"></i>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Phone Verification Section -->
                        <div class="verification-section {{ auth()->user()->hasVerifiedPhone() ? 'completed' : 'pending' }}">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex align-items-center">
                                        <i class="las la-phone verification-icon phone"></i>
                                        <div class="ms-3">
                                            <h5 class="mb-1">{{ translate('Phone Verification') }}</h5>
                                            <p class="mb-1 text-muted">{{ auth()->user()->country_code }}{{ auth()->user()->phone }}</p>
                                            <span class="status-badge {{ auth()->user()->hasVerifiedPhone() ? 'status-verified' : 'status-pending' }}">
                                                @if(auth()->user()->hasVerifiedPhone())
                                                    <i class="las la-check-circle me-1"></i>{{ translate('Verified') }}
                                                @else
                                                    <i class="las la-clock me-1"></i>{{ translate('Pending') }}
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    @if(!auth()->user()->hasVerifiedPhone())
                                        <a href="{{ route('phone.verification') }}" class="btn verify-btn" {{ !auth()->user()->hasVerifiedEmail() ? 'disabled' : '' }}>
                                            {{ translate('Verify Phone') }}
                                        </a>
                                    @else
                                        <i class="las la-check-circle text-success" style="font-size: 2rem;"></i>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Continue Button -->
                        @if(auth()->user()->isFullyVerified())
                            <div class="text-center mt-4">
                                <a href="{{ auth()->user()->user_type == 'seller' ? route('seller.dashboard') : route('dashboard') }}" 
                                   class="btn continue-btn">
                                    <i class="las la-arrow-right me-2"></i>
                                    {{ translate('Continue to Dashboard') }}
                                </a>
                            </div>
                        @else
                            <div class="text-center mt-4">
                                <p class="text-muted">
                                    {{ translate('Complete both verifications to access your dashboard') }}
                                </p>
                            </div>
                        @endif

                        <!-- Logout Option -->
                        <div class="text-center mt-4">
                            <a href="{{ route('logout') }}" class="logout-link"
                               onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                {{ translate('Logout') }}
                            </a>
                            <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                @csrf
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
