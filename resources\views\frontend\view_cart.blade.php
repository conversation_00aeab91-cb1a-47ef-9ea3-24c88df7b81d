@php
    $theme = get_setting('homepage_select');
@endphp

@extends('frontend.'.$theme.'.layouts.app')

@section('content')
    @php
        $total = 0;
    @endphp
    <div class="container my-5">
        <div class="cart__section--inner">
            <!--<form action="#">-->
                <h2 class="cart__title mb-40">Shopping Cart</h2>
                <div id="cart-summary">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="cart__table">
                                <table class="cart__table--inner">
                                    <thead class="cart__table--header">
                                    <tr class="cart__table--header__items">
                                        <th class="cart__table--header__list">Product</th>
                                        <th class="cart__table--header__list">Price</th>
                                        <th class="cart__table--header__list">Quantity</th>
                                        <th class="cart__table--header__list">Total</th>
                                    </tr>
                                    </thead>
                                    <tbody class="cart__table--body">
                                    @if( $carts && count($carts) > 0 )
                                        @foreach ($carts as $key => $cartItem)
                                            @php
                                                $product = get_single_product($cartItem['product_id']);
                                                $product_stock = $product->stocks->where('variant', $cartItem['variation'])->first();
                                                // $total = $total + ($cartItem['price']  + $cartItem['tax']) * $cartItem['quantity'];
                                                $total = $total + cart_product_price($cartItem, $product, false) * $cartItem['quantity'];
                                                $product_name_with_choice = $product->getTranslation('name');
                                                if ($cartItem['variation'] != null) {
                                                    $product_name_with_choice = $product->getTranslation('name').' - '.$cartItem['variation'];
                                                }
                                                    $product_url = route('product', $product->slug);
                                            @endphp
                                            <tr class="cart__table--body__items">
                                                <td class="cart__table--body__list">
                                                    <!-- Remove From Cart -->
                                                    <div class="cart__product d-flex align-items-center">
                                                        <button class="cart__remove--btn" aria-label="search button" type="button" onclick="removeFromCartView(event, {{ $cartItem['id'] }})">
                                                            <svg fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16px" height="16px"><path d="M 4.7070312 3.2929688 L 3.2929688 4.7070312 L 10.585938 12 L 3.2929688 19.292969 L 4.7070312 20.707031 L 12 13.414062 L 19.292969 20.707031 L 20.707031 19.292969 L 13.414062 12 L 20.707031 4.7070312 L 19.292969 3.2929688 L 12 10.585938 L 4.7070312 3.2929688 z"></path></svg>
                                                        </button>
                                                        <div class="cart__thumbnail">
                                                            <a href="{{ $product_url }}"><img class="border-radius-5" src="{{ uploaded_asset($product->thumbnail_img) }}" alt="{{ $product->getTranslation('name')  }}" onerror="this.onerror=null;this.src='{{ static_asset('assets/img/placeholder.jpg') }}';"></a>
                                                        </div>
                                                        <div class="cart__content">
                                                            <h4 class="cart__content--title"><a href="{{ $product_url }}">{{ $product_name_with_choice }}</a></h4>
                                                            <span class="cart__content--variant">
                                                                {{ $product->fulfillment_type == 'cloudmart' ? 'Ships from Cloud Mart' : 'Ships from Seller' }}
                                                            </span>
                                                            {{--                                                    <span class="cart__content--variant">COLOR: Blue</span>--}}
                                                            {{--                                                    <span class="cart__content--variant">WEIGHT: 2 Kg</span>--}}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="cart__table--body__list">
                                                    <span class="cart__price">{{ cart_product_price($cartItem, $product, true, false) }}</span>
                                                </td>
                                                <td class="cart__table--body__list">
                                                    @if ($cartItem['digital'] != 1 && $product->auction_product == 0)
                                                        <div class="quantity__box">
                                                            <button
                                                                class="quantity__value quickview__value--quantity decrease"
                                                                type="button" data-type="plus"
                                                                data-field="quantity[{{ $cartItem['id'] }}]">
                                                                -
                                                            </button>
                                                            <label>
                                                                <input type="number" name="quantity[{{ $cartItem['id'] }}]"
                                                                       class="quantity__number quickview__value--number"
                                                                       placeholder="1" value="{{ $cartItem['quantity'] }}"
                                                                       min="{{ $product->min_qty }}"
                                                                       max="{{ $product_stock->qty }}"
                                                                       onchange="updateQuantity({{ $cartItem['id'] }}, this)" >
                                                            </label>
                                                            <button
                                                                class="quantity__value quickview__value--quantity increase"
                                                                type="button" data-type="minus"
                                                                data-field="quantity[{{ $cartItem['id'] }}]">
                                                                +
                                                            </button>
                                                        </div>
                                                    @elseif($product->auction_product == 1)
                                                        <span class="fw-700 fs-14">1</span>
                                                    @endif
                                                </td>
                                                <td class="cart__table--body__list">
                                                    @php
                                                        $formatted_price = cart_product_price($cartItem, $product, false, false);
                                                        $productTotal = (float)$formatted_price * $cartItem['quantity'];
                                                    @endphp
                                                    <span class="cart__price end">{{ format_price(convert_price($productTotal)) }}</span>
                                                </td>
                                            </tr>
                                        @endforeach
                                    @endif
                                    </tbody>
                                </table>
                                <div class="continue__shopping d-flex justify-content-between">
                                    <a class="continue__shopping--link" href="{{ route('home') }}">Continue shopping</a>
                                    {{--                            <button class="continue__shopping--clear" type="submit">Clear Cart</button>--}}
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="cart__summary border-radius-10">
                                <div class="coupon__code mb-30">
                                    <h3 class="coupon__code--title">Coupon</h3>
                                    <p class="coupon__code--desc">Enter your coupon code if you have one.</p>
                                    <div class="coupon__code--field d-flex">
                                        <label>
                                            <input class="coupon__code--field__input border-radius-5" placeholder="Coupon code" type="text">
                                        </label>
                                        <button class="coupon__code--field__btn primary__btn" type="submit">Apply Coupon</button>
                                    </div>
                                </div>
                                {{--                        <div class="cart__note mb-20">--}}
                                {{--                            <h3 class="cart__note--title">Note</h3>--}}
                                {{--                            <p class="cart__note--desc">Add special instructions for your seller...</p>--}}
                                {{--                            <textarea class="cart__note--textarea border-radius-5"></textarea>--}}
                                {{--                        </div>--}}
                                <div class="cart__summary--total mb-20">
                                    <table class="cart__summary--total__table">
                                        <tbody>
                                        <tr class="cart__summary--total__list">
                                            <td class="cart__summary--total__title text-left">SUBTOTAL</td>
                                            <td class="cart__summary--amount text-right">{{ single_price($total) }}</td>
                                        </tr>
                                        <tr class="cart__summary--total__list">
                                            <td class="cart__summary--total__title text-left">GRAND TOTAL</td>
                                            <td class="cart__summary--amount text-right">{{ single_price($total) }}</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="cart__summary--footer">
                                    <p class="cart__summary--footer__desc">Shipping &amp; taxes calculated at checkout</p>
                                    <ul class="d-flex justify-content-between">
                                        @if(Auth::check())
                                            <a href="{{ route('checkout.shipping_info') }}" class="cart__summary--footer__btn primary__btn checkout">
                                                {{ translate('Continue to Shipping')}}
                                            </a>
                                        @else
                                            <button class="cart__summary--footer__btn primary__btn checkout" onclick="showLoginModal()" type="button">{{ translate('Continue to Shipping')}}</button>
                                        @endif
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <!--</form>-->
        </div>
    </div>

@endsection
