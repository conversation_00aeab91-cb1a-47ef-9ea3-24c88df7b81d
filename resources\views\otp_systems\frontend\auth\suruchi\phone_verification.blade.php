@extends('frontend.suruchi.layouts.app')

@section('content')
<div class="login__section section--padding">
    <div class="container">
        <form class="form-default" action="{{ route('phone.verification.submit') }}" method="POST">
            @csrf
            <div class="login__section--inner">
                <div class="row">
                    <div class="col-md-3 col-lg-3 col-sm-0"></div>
                    <div class="col-md-6 col-lg-6">
                        <div class="account__login">
                            <!-- Title Section -->
                            <div class="account__login--header mb-25">
                                <h2 class="account__login--header__title h3 mb-10">{{ translate('Phone Verification') }}</h2>
                                <p class="account__login--header__desc">
                                    {{ translate('Please enter the 6-digit verification code sent to') }} 
                                    <strong>{{ $user->phone }}</strong>
                                </p>
                            </div>

                            <!-- Form Section -->
                            <div class="account__login--inner">
                                <!-- Verification Code Field -->
                                <div class="form-group mb-3">
                                    <input class="account__login--input text-center" 
                                           name="verification_code" 
                                           placeholder="{{ translate('Enter 6-digit code') }}" 
                                           type="text" 
                                           maxlength="6"
                                           style="font-size: 18px; letter-spacing: 2px;"
                                           required>
                                    @error('verification_code')
                                    <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- Submit Button -->
                                <button class="account__login--btn primary__btn mb-15" type="submit">
                                    {{ translate('Verify Phone') }}
                                </button>

                                <!-- Resend Code -->
                                <div class="text-center mb-15">
                                    <p class="mb-2">{{ translate("Didn't receive the code?") }}</p>
                                    <button type="button" id="resend-btn" class="btn btn-link p-0 text-primary">
                                        {{ translate('Resend Code') }}
                                    </button>
                                    <div id="resend-timer" class="text-muted mt-2" style="display: none;">
                                        {{ translate('Resend available in') }} <span id="timer">60</span> {{ translate('seconds') }}
                                    </div>
                                </div>

                                <!-- Back to Home -->
                                <div class="text-center">
                                    <a href="{{ route('home') }}" class="text-muted">
                                        <i class="las la-arrow-left"></i> {{ translate('Back to Home') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
$(document).ready(function() {
    let resendTimer = 60;
    let timerInterval;

    // Start timer on page load
    startResendTimer();

    // Resend code functionality
    $('#resend-btn').click(function() {
        $.ajax({
            url: "{{ route('phone.verification.resend') }}",
            type: "GET",
            success: function(response) {
                if (response.status === 'success') {
                    alert(response.message);
                    startResendTimer();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('{{ translate("Error sending code. Please try again.") }}');
            }
        });
    });

    function startResendTimer() {
        resendTimer = 60;
        $('#resend-btn').prop('disabled', true).addClass('text-muted').removeClass('text-primary');
        $('#resend-timer').show();
        
        timerInterval = setInterval(function() {
            resendTimer--;
            $('#timer').text(resendTimer);
            
            if (resendTimer <= 0) {
                clearInterval(timerInterval);
                $('#resend-btn').prop('disabled', false).removeClass('text-muted').addClass('text-primary');
                $('#resend-timer').hide();
            }
        }, 1000);
    }

    // Auto-format verification code input
    $('input[name="verification_code"]').on('input', function() {
        let value = $(this).val().replace(/\D/g, ''); // Remove non-digits
        if (value.length > 6) {
            value = value.substring(0, 6);
        }
        $(this).val(value);
    });

    // Auto-submit when 6 digits are entered
    $('input[name="verification_code"]').on('input', function() {
        if ($(this).val().length === 6) {
            // Small delay to allow user to see the complete code
            setTimeout(function() {
                $('.form-default').submit();
            }, 500);
        }
    });
});
</script>
@endsection
