@extends('frontend.suruchi.layouts.app')

@section('content')
<div class="login__section section--padding">
    <div class="container">
        <form class="form-default" action="{{ route('password.update.phone') }}" method="POST">
            @csrf
            <div class="login__section--inner">
                <div class="row">
                    <div class="col-md-3 col-lg-3 col-sm-0"></div>
                    <div class="col-md-6 col-lg-6">
                        <div class="account__login">
                            <!-- Title Section -->
                            <div class="account__login--header mb-25">
                                <h2 class="account__login--header__title h3 mb-10">{{ translate('Reset Password') }}</h2>
                                <p class="account__login--header__desc">
                                    {{ translate('Enter the verification code sent to your phone and your new password') }}
                                </p>
                            </div>

                            <!-- Form Section -->
                            <div class="account__login--inner">
                                <!-- Verification Code Field -->
                                <div class="form-group mb-3">
                                    <input class="account__login--input" 
                                           name="verification_code" 
                                           placeholder="{{ translate('Enter 6-digit verification code') }}" 
                                           type="text" 
                                           maxlength="6"
                                           required>
                                    @error('verification_code')
                                    <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- New Password Field -->
                                <div class="form-group mb-3">
                                    <input class="account__login--input" 
                                           name="password" 
                                           placeholder="{{ translate('New Password') }}" 
                                           type="password" 
                                           required>
                                    @error('password')
                                    <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- Confirm Password Field -->
                                <div class="form-group mb-3">
                                    <input class="account__login--input" 
                                           name="password_confirmation" 
                                           placeholder="{{ translate('Confirm New Password') }}" 
                                           type="password" 
                                           required>
                                </div>

                                <!-- Submit Button -->
                                <button class="account__login--btn primary__btn mb-15" type="submit">
                                    {{ translate('Reset Password') }}
                                </button>

                                <!-- Back to Login -->
                                <div class="text-center">
                                    <a href="{{ route('user.login') }}" class="text-muted">
                                        <i class="las la-arrow-left"></i> {{ translate('Back to Login') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
$(document).ready(function() {
    // Auto-format verification code input
    $('input[name="verification_code"]').on('input', function() {
        let value = $(this).val().replace(/\D/g, ''); // Remove non-digits
        if (value.length > 6) {
            value = value.substring(0, 6);
        }
        $(this).val(value);
    });
});
</script>
@endsection
