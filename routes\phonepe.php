<?php

//PhonePe

use App\Http\Controllers\Payment\PhonePeController;

Route::controller(PhonePeController::class)->group(function () {
    Route::get('/phonepe/pay', 'pay')->name('phonepe.pay');
    Route::post('/phonepe/callback', 'callback')->name('phonepe.callback');
});

//Admin
Route::group(['prefix' =>'admin', 'middleware' => ['auth', 'admin']], function(){
    Route::controller(PhonePeController::class)->group(function () {
        Route::get('/phonepe_configuration', 'credentials_index')->name('phonepe.index');
        Route::post('/phonepe_configuration_update', 'update_credentials')->name('phonepe.update_credentials');
    });
});
